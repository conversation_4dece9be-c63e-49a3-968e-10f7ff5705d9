Z:\alwan6\app\src\main\java\com\alwan\kids2025\GalleryActivity.java:83: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    name.toLowerCase().endsWith(".png") || name.toLowerCase().endsWith(".jpg"));
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\GalleryActivity.java:83: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    name.toLowerCase().endsWith(".png") || name.toLowerCase().endsWith(".jpg"));
                                                                ~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

Z:\alwan6\app\src\main\java\com\alwan\kids2025\MyFirebaseMessagingService.java:15: Warning: Apps that use Firebase Cloud Messaging should implement onNewToken() in order to observe token changes [MissingFirebaseInstanceTokenRefresh]
public class MyFirebaseMessagingService extends FirebaseMessagingService {
             ~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingFirebaseInstanceTokenRefresh":
   Apps that use Firebase Cloud Messaging should implement the
   FirebaseMessagingService#onNewToken() callback in order to observe token
   changes.

   https://firebase.google.com/docs/cloud-messaging/android/client#monitor-token-generation

Z:\alwan6\app\src\main\res\values-night\styles.xml:16: Error: android:windowLightNavigationBar requires API level 27 (current min is 23) [NewApi]
        <item name="android:windowLightNavigationBar">false</item>
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NewApi":
   This check scans through all the Android API calls in the application and
   warns about any calls that are not available on all versions targeted by
   this application (according to its minimum SDK attribute in the manifest).

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

   If you are deliberately setting android: attributes in style definitions,
   make sure you place this in a values-vNN folder in order to avoid running
   into runtime conflicts on certain devices where manufacturers have added
   custom attributes whose ids conflict with the new ones on later platforms.

   Similarly, you can use tools:targetApi="11" in an XML file to indicate that
   the element will only be inflated in an adequate context.

Z:\alwan6\app\build.gradle:8: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdkVersion 34
        ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application or sdk runs on a version of Android that is more
   recent than your targetSdkVersion specifies that it has been tested with,
   various compatibility modes kick in. This ensures that your application
   continues to work, but it may look out of place. For example, if the
   targetSdkVersion is less than 14, your app may get an option button in the
   UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

Z:\alwan6\app\src\main\res\layout\activity_categories.xml:63: Warning: Attribute autoSizeTextType is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:autoSizeTextType="uniform"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:64: Warning: Attribute autoSizeMinTextSize is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:autoSizeMinTextSize="18sp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:65: Warning: Attribute autoSizeMaxTextSize is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:autoSizeMaxTextSize="28sp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:66: Warning: Attribute autoSizeStepGranularity is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:autoSizeStepGranularity="2sp" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:81: Warning: Attribute autoSizeTextType is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:autoSizeTextType="uniform"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:82: Warning: Attribute autoSizeMinTextSize is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:autoSizeMinTextSize="12sp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:83: Warning: Attribute autoSizeMaxTextSize is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:autoSizeMaxTextSize="16sp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:84: Warning: Attribute autoSizeStepGranularity is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:autoSizeStepGranularity="1sp" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\font\blabeloo.xml:4: Warning: Attribute fontStyle is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
        android:fontStyle="normal"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\font\blabeloo.xml:5: Warning: Attribute fontWeight is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
        android:fontWeight="400"
        ~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\font\blabeloo.xml:6: Warning: Attribute font is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
        android:font="@font/blabeloo_regular" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedAttribute":
   This check finds attributes set in XML files that were introduced in a
   version newer than the oldest version targeted by your application (with
   the minSdkVersion attribute).

   This is not an error; the application will simply ignore the attribute.
   However, if the attribute is important to the appearance or functionality
   of your application, you should consider finding an alternative way to
   achieve the same result with only available attributes, and then you can
   optionally create a copy of the layout in a layout-vNN folder which will be
   used on API NN or higher where you can take advantage of the newer
   attribute.

   Note: This check does not only apply to attributes. For example, some tags
   can be unused too, such as the new <tag> element in layouts introduced in
   API 21.

Z:\alwan6\app\src\main\java\com\alwan\kids2025\LanguageManager.java:94: Warning: Found dynamic locale changes, but did not find corresponding Play Core library calls for downloading languages and splitting by language is not disabled in the bundle configuration [AppBundleLocaleChanges]
            configuration.setLocale(locale);
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "AppBundleLocaleChanges":
   When changing locales at runtime (e.g. to provide an in-app language
   switcher), the Android App Bundle must be configured to not split by locale
   or the Play Core library must be used to download additional locales at
   runtime.

   https://developer.android.com/guide/app-bundle/configure-base#handling_language_changes

Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseActivity.java:55: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.mainmenu:
                 ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseActivity.java:62: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.categories:
                 ~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseActivity.java:66: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.gallery:
                 ~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseActivity.java:70: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.favorites:
                 ~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseActivity.java:74: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.settings:
                 ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseActivity.java:78: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.about:
                 ~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseActivity.java:82: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.share:
                 ~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseActivity.java:85: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.rate:
                 ~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseActivity.java:90: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.support:
                 ~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseActivity.java:104: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.ads:
                 ~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseActivity.java:107: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.privacyPolicy:
                 ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseLocalizedActivity.java:90: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.mainmenu:
                 ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseLocalizedActivity.java:97: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.categories:
                 ~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseLocalizedActivity.java:101: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.gallery:
                 ~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseLocalizedActivity.java:105: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.favorites:
                 ~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseLocalizedActivity.java:109: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.settings:
                 ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseLocalizedActivity.java:113: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.about:
                 ~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseLocalizedActivity.java:117: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.share:
                 ~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseLocalizedActivity.java:120: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.rate:
                 ~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseLocalizedActivity.java:125: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.support:
                 ~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseLocalizedActivity.java:139: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.ads:
                 ~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\BaseLocalizedActivity.java:142: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.privacyPolicy:
                 ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\Categories.java:134: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.c_1:
                 ~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\Categories.java:137: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.c_2:
                 ~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\Categories.java:140: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.c_3:
                 ~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\Categories.java:143: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.c_4:
                 ~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\Categories.java:146: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.c_5:
                 ~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\Categories.java:149: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.c_6:
                 ~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:353: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.action_share:
                 ~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:356: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.action_save:
                 ~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:359: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.action_mute:
                 ~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:373: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.action_undo:
                 ~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:475: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.white -> {
                 ~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:479: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.black -> {
                 ~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:483: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.gray -> {
                 ~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:487: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.light_orange -> {
                 ~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:491: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.brown -> {
                 ~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:495: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.yellow -> {
                 ~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:499: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.deep_blue -> {
                 ~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:503: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.light_blue -> {
                 ~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:507: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.deep_purple -> {
                 ~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:511: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.light_purple -> {
                 ~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:515: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.deep_green -> {
                 ~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:519: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.light_green -> {
                 ~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:523: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.deep_pink -> {
                 ~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:527: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.light_pink -> {
                 ~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:531: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.red -> {
                 ~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:535: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.deep_orange -> {
                 ~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:539: Warning: Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements [NonConstantResourceId]
            case R.id.select_color -> {
                 ~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NonConstantResourceId":
   Avoid the usage of resource IDs where constant expressions are required.

   A future version of the Android Gradle Plugin will generate R classes with
   non-constant IDs in order to improve the performance of incremental
   compilation.

Z:\alwan6\app\src\main\res\drawable\pressed_no_corners.xml:10: Warning: This item is unreachable because a previous item (item #1) is a more general match than this one [StateListReachable]
    <item android:state_pressed="true" >
    ^
    Z:\alwan6\app\src\main\res\drawable\pressed_no_corners.xml:3: Earlier item which masks item

   Explanation for issues of type "StateListReachable":
   In a selector, only the last child in the state list should omit a state
   qualifier. If not, all subsequent items in the list will be ignored since
   the given item will match all.

Z:\alwan6\app\src\main\res\drawable\premium_nav_pattern.xml:3: Warning: Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more [VectorRaster]
    android:width="320dp"
                   ~~~~~

   Explanation for issues of type "VectorRaster":
   Vector icons require API 21 or API 24 depending on used features, but when
   minSdkVersion is less than 21 or 24 and Android Gradle plugin 1.4 or higher
   is used, a vector drawable placed in the drawable folder is automatically
   moved to drawable-anydpi-v21 or drawable-anydpi-v24 and bitmap images are
   generated for different screen resolutions for backwards compatibility.

   However, there are some limitations to this raster image generation, and
   this lint check flags elements and attributes that are not fully supported.
   You should manually check whether the generated output is acceptable for
   those older devices.

Z:\alwan6\app\build.gradle:4: Warning: A newer version of compileSdkVersion than 34 is available: 35 [GradleDependency]
    compileSdk 34
    ~~~~~~~~~~~~~
Z:\alwan6\app\build.gradle:50: Warning: A newer version of com.google.firebase:firebase-bom than 32.8.1 is available: 33.14.0 [GradleDependency]
    implementation platform('com.google.firebase:firebase-bom:32.8.1')
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\build.gradle:61: Warning: A newer version of com.google.android.play:review than 2.0.1 is available: 2.0.2 [GradleDependency]
    implementation 'com.google.android.play:review:2.0.1' // Reverted to a stable version
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\build.gradle:64: Warning: A newer version of androidx.preference:preference than 1.2.0 is available: 1.2.1 [GradleDependency]
    implementation 'androidx.preference:preference:1.2.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\build.gradle:66: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.0 [GradleDependency]
    implementation 'androidx.appcompat:appcompat:1.6.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\build.gradle:67: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0 [GradleDependency]
    implementation 'androidx.core:core-ktx:1.10.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\build.gradle:68: Warning: A newer version of androidx.preference:preference than 1.2.0 is available: 1.2.1 [GradleDependency]
    implementation 'androidx.preference:preference:1.2.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\build.gradle:70: Warning: A newer version of com.onesignal:OneSignal than 4.8.6 is available: 4.8.7 [GradleDependency]
    implementation 'com.onesignal:OneSignal:4.8.6'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\build.gradle:73: Warning: A newer version of androidx.core:core than 1.12.0 is available: 1.16.0 [GradleDependency]
    implementation 'androidx.core:core:1.12.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\build.gradle:74: Warning: A newer version of androidx.activity:activity than 1.8.0 is available: 1.10.1 [GradleDependency]
    implementation 'androidx.activity:activity:1.8.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

Z:\alwan6\app\src\main\java\com\alwan\kids2025\CategoryItems.java:115: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
                imagesArray = getResources().getIdentifier("Thumbnails1", "array", getPackageName());
                                             ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\CategoryItems.java:119: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
                imagesArray = getResources().getIdentifier("Thumbnails2", "array", getPackageName());
                                             ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\CategoryItems.java:123: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
                imagesArray = getResources().getIdentifier("Thumbnails3", "array", getPackageName());
                                             ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\CategoryItems.java:127: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
                imagesArray = getResources().getIdentifier("Thumbnails4", "array", getPackageName());
                                             ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\CategoryItems.java:131: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
                imagesArray = getResources().getIdentifier("Thumbnails5", "array", getPackageName());
                                             ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\CategoryItems.java:135: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
                imagesArray = getResources().getIdentifier("Thumbnails6", "array", getPackageName());
                                             ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:607: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
            int backgroundResourceId = this.getResources().getIdentifier("background_" + randomNumber, "raw", this.getPackageName());
                                                           ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:650: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
            int id = getResources().getIdentifier("gp" + code + "_" + position, "drawable", getPackageName());
                                    ~~~~~~~~~~~~~

   Explanation for issues of type "DiscouragedApi":
   Discouraged APIs are allowed and are not deprecated, but they may be unfit
   for common use (e.g. due to slow performance or subtle behavior).

Z:\alwan6\app\src\main\res\layout\activity_about.xml:177: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@color/flowers_color" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_about.xml:202: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@color/cartoons_color" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_about.xml:227: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@color/animals_color" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_about.xml:251: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@color/modern_accent" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:167: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                    android:tint="@android:color/white" />
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:265: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                    android:tint="@android:color/white" />
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:370: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                    android:tint="@android:color/white" />
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:463: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                    android:tint="@android:color/white" />
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:565: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                    android:tint="@android:color/white" />
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:658: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                    android:tint="@android:color/white" />
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:58: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="@android:color/white" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:105: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="@color/text_secondary"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:152: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@android:color/white" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:222: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@color/text_secondary" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:262: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@android:color/white" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:299: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/modern_accent" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:58: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="@android:color/white" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:105: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="@color/text_secondary"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:152: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@android:color/white" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:215: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/text_secondary" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:255: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@android:color/white" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:58: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="@android:color/white" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:99: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="@android:color/white" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:139: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="@android:color/white" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:179: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="@android:color/white" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:219: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="@android:color/white" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:42: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="@android:color/white" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:118: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@android:color/white" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:151: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/text_secondary" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:210: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@android:color/white" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:243: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/text_secondary" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:302: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@android:color/white" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:335: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/text_secondary" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:365: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@android:color/white" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:398: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/text_secondary" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:427: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@android:color/white" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:460: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/text_secondary" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:63: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/modern_primary" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:159: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/cartoons_color" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:281: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/animals_color" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:338: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@color/text_secondary" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:386: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="@color/text_secondary" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:423: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/nature_color" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:463: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/text_secondary" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:492: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/text_secondary" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:527: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/modern_accent" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:625: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@color/foods_color" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:43: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="@android:color/white" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:92: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="@android:color/white" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:141: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="@android:color/white" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation_bar.xml:49: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="@android:color/white" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation_bar.xml:100: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="@android:color/white" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation_bar.xml:151: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="@android:color/white" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\dialog_exit_confirmation.xml:40: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                    android:tint="@color/modern_accent" />
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:68: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@android:color/white" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:128: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@android:color/white" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:188: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@android:color/white" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:248: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@android:color/white" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:308: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="@android:color/white" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\nav_header_drawer.xml:84: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
        android:tint="@android:color/white" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\nav_header_drawer.xml:94: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
        android:tint="@android:color/white" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:117: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="@android:color/white" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:126: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="@android:color/white" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:135: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="@android:color/white" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:31: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="#4CAF50" />
            ~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:61: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="#AB47BC" />
            ~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:91: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="#FF6B9D" />
            ~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:121: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="#6B7280" />
            ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseAppTint":
   ImageView or ImageButton uses android:tint instead of app:tint

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

Z:\alwan6\app\src\main\res\values\strings.xml:33: Error: "sound_settings" is not translated in "ar" (Arabic) [MissingTranslation]
    <string name="sound_settings">Sound Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:34: Error: "display_settings" is not translated in "ar" (Arabic) [MissingTranslation]
    <string name="display_settings">Display Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:35: Error: "language_quality" is not translated in "ar" (Arabic) [MissingTranslation]
    <string name="language_quality">Language &amp; Quality</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:36: Error: "app_language" is not translated in "ar" (Arabic) [MissingTranslation]
    <string name="app_language">App Language</string>
            ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:182: Error: "share_text_english" is not translated in "ar" (Arabic) [MissingTranslation]
    <string name="share_text_english">Let your child express their imagination and draw with beautiful colors in a fun and safe way!\nDownload the app now and let them enjoy hours of creativity and coloring 🎉💛\n</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingTranslation":
   If an application has more than one locale, then all the strings declared
   in one language should also be translated in all other languages.

   If the string should not be translated, you can add the attribute
   translatable="false" on the <string> element, or you can define all your
   non-translatable strings in a resource file called donottranslate.xml. Or,
   you can ignore the issue with a tools:ignore="MissingTranslation"
   attribute.

   You can tell lint (and other tools) which language is the default language
   in your res/values/ folder by specifying tools:locale="languageCode" for
   the root <resources> element in your resource file. (The tools prefix
   refers to the namespace declaration http://schemas.android.com/tools.)

Z:\alwan6\app\src\main\AndroidManifest.xml:77: Warning: Exported service does not require permission [ExportedService]
        <service
         ~~~~~~~

   Explanation for issues of type "ExportedService":
   Exported services (services which either set exported=true or contain an
   intent-filter and do not specify exported=false) should define a permission
   that an entity must have in order to launch the service or bind to it.
   Without this, any application can use this service.

   https://goo.gle/ExportedService

Z:\alwan6\app\src\main\AndroidManifest.xml:13: Warning: The attribute android:allowBackup is deprecated from Android 12 and higher and may be removed in future versions. Consider adding the attribute android:dataExtractionRules specifying an @xml resource which configures cloud backups and device transfers on Android 12 and higher. [DataExtractionRules]
        android:allowBackup="false"
                             ~~~~~

   Explanation for issues of type "DataExtractionRules":
   Before Android 12, the attributes android:allowBackup and
   android:fullBackupContent were used to configure all forms of backup,
   including cloud backups, device-to-device transfers and adb backup.

   In Android 12 and higher, these attributes have been deprecated and will
   only apply to cloud backups. You should instead use the attribute
   android:dataExtractionRules, specifying an @xml resource that configures
   which files to back up, for cloud backups and for device-to-device
   transfers, separately. If your minSdkVersion supports older versions,
   you'll still want to specify an android:fullBackupContent resource if the
   default behavior is not right for your app.

   https://developer.android.com/about/versions/12/backup-restore#xml-changes
   https://goo.gle/DataExtractionRules

Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:726: Warning: Avoid object allocations during draw/layout operations (preallocate and reuse instead) [DrawAllocation]
            DisplayMetrics metrics = new DisplayMetrics();
                                     ~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DrawAllocation":
   You should avoid allocating objects during a drawing or layout operation.
   These are called frequently, so a smooth UI can be interrupted by garbage
   collection pauses caused by the object allocations.

   The way this is generally handled is to allocate the needed objects up
   front and to reuse them for each drawing operation.

   Some methods allocate memory on your behalf (such as Bitmap.create), and
   these should be handled in the same way.

Z:\alwan6\app\src\main\java\com\alwan\kids2025\CategoryItems.java:163: Warning: This TypedArray should be recycled after use with #recycle() [Recycle]
        TypedArray imgs = getResources().obtainTypedArray(imagesArray);
                                         ~~~~~~~~~~~~~~~~

   Explanation for issues of type "Recycle":
   Many resources, such as TypedArrays, VelocityTrackers, etc., should be
   recycled (with a recycle() call) after use. This lint check looks for
   missing recycle() calls.

Z:\alwan6\app\src\main\java\com\alwan\kids2025\LanguageManager.java:98: Warning: Unnecessary; Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 is never true here (SDK_INT = 23) [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\LanguageManager.java:152: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\drawable-v21: Warning: This folder configuration (v21) is unnecessary; minSdkVersion is 23. Merge all the resources in this folder into drawable. [ObsoleteSdkInt]

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

Z:\alwan6\app\src\main\res\layout\activity_about.xml:166: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                        <LinearLayout
                         ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_about.xml:191: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                        <LinearLayout
                         ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_about.xml:216: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                        <LinearLayout
                         ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_about.xml:241: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                        <LinearLayout
                         ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:140: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                        <LinearLayout
                         ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:210: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                        <LinearLayout
                         ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:250: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                        <LinearLayout
                         ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:287: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:140: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                        <LinearLayout
                         ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:203: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:243: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:51: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:147: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:269: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:411: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:515: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:613: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                    <LinearLayout
                     ~~~~~~~~~~~~

   Explanation for issues of type "UseCompoundDrawables":
   A LinearLayout which contains an ImageView and a TextView can be more
   efficiently handled as a compound drawable (a single TextView, using the
   drawableTop, drawableLeft, drawableRight and/or drawableBottom attributes
   to draw one or more images adjacent to the text).

   If the two widgets are offset from each other with margins, this can be
   replaced with a drawablePadding attribute.

   There's a lint quickfix to perform this conversion in the Eclipse plugin.

Z:\alwan6\app\src\main\res\drawable\ic_animals_modern.xml:9: Warning: Very long vector path (1015 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
        android:pathData="M4.5,9C5.33,9 6,8.33 6,7.5C6,6.67 5.33,6 4.5,6C3.67,6 3,6.67 3,7.5C3,8.33 3.67,9 4.5,9ZM9,9C9.83,9 10.5,8.33 10.5,7.5C10.5,6.67 9.83,6 9,6C8.17,6 7.5,6.67 7.5,7.5C7.5,8.33 8.17,9 9,9ZM15,9C15.83,9 16.5,8.33 16.5,7.5C16.5,6.67 15.83,6 15,6C14.17,6 13.5,6.67 13.5,7.5C13.5,8.33 14.17,9 15,9ZM19.5,9C20.33,9 21,8.33 21,7.5C21,6.67 20.33,6 19.5,6C18.67,6 18,6.67 18,7.5C18,8.33 18.67,9 19.5,9ZM17.34,14.67C18.03,14.67 18.67,14.03 18.67,13.34C18.67,11.3 17.36,9.65 15.5,9.65C13.64,9.65 12.33,11.3 12.33,13.34C12.33,14.03 12.97,14.67 13.66,14.67C14.35,14.67 15,14.03 15,13.34C15,12.8 15.22,12.33 15.5,12.33C15.78,12.33 16,12.8 16,13.34C16,14.03 16.64,14.67 17.34,14.67ZM6.34,14.67C7.03,14.67 7.67,14.03 7.67,13.34C7.67,11.3 6.36,9.65 4.5,9.65C2.64,9.65 1.33,11.3 1.33,13.34C1.33,14.03 1.97,14.67 2.66,14.67C3.35,14.67 4,14.03 4,13.34C4,12.8 4.22,12.33 4.5,12.33C4.78,12.33 5,12.8 5,13.34C5,14.03 5.64,14.67 6.34,14.67ZM12,16C14.21,16 16,17.79 16,20C16,20.55 15.55,21 15,21L9,21C8.45,21 8,20.55 8,20C8,17.79 9.79,16 12,16Z" />
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\drawable\ic_flowers_modern.xml:9: Warning: Very long vector path (801 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
        android:pathData="M12,2C13.1,2 14,2.9 14,4C14,5.1 13.1,6 12,6C10.9,6 10,5.1 10,4C10,2.9 10.9,2 12,2ZM21,9C22.1,9 23,9.9 23,11C23,12.1 22.1,13 21,13C19.9,13 19,12.1 19,11C19,9.9 19.9,9 21,9ZM3,9C4.1,9 5,9.9 5,11C5,12.1 4.1,13 3,13C1.9,13 1,12.1 1,11C1,9.9 1.9,9 3,9ZM15.5,6.5C16.6,6.5 17.5,7.4 17.5,8.5C17.5,9.6 16.6,10.5 15.5,10.5C14.4,10.5 13.5,9.6 13.5,8.5C13.5,7.4 14.4,6.5 15.5,6.5ZM8.5,6.5C9.6,6.5 10.5,7.4 10.5,8.5C10.5,9.6 9.6,10.5 8.5,10.5C7.4,10.5 6.5,9.6 6.5,8.5C6.5,7.4 7.4,6.5 8.5,6.5ZM12,7C14.8,7 17,9.2 17,12C17,14.8 14.8,17 12,17C9.2,17 7,14.8 7,12C7,9.2 9.2,7 12,7ZM12,9C10.3,9 9,10.3 9,12C9,13.7 10.3,15 12,15C13.7,15 15,13.7 15,12C15,10.3 13.7,9 12,9ZM12,18L12,22L11,22C10.4,22 10,21.6 10,21L10,19C10,18.4 10.4,18 11,18L12,18ZM13,18L14,18C14.6,18 15,18.4 15,19L15,21C15,21.6 14.6,22 14,22L13,22L13,18Z" />
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\drawable\ic_foods_modern.xml:9: Warning: Very long vector path (801 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
        android:pathData="M12,2C13.1,2 14,2.9 14,4C14,5.1 13.1,6 12,6C10.9,6 10,5.1 10,4C10,2.9 10.9,2 12,2ZM21,9C22.1,9 23,9.9 23,11C23,12.1 22.1,13 21,13C19.9,13 19,12.1 19,11C19,9.9 19.9,9 21,9ZM3,9C4.1,9 5,9.9 5,11C5,12.1 4.1,13 3,13C1.9,13 1,12.1 1,11C1,9.9 1.9,9 3,9ZM15.5,6.5C16.6,6.5 17.5,7.4 17.5,8.5C17.5,9.6 16.6,10.5 15.5,10.5C14.4,10.5 13.5,9.6 13.5,8.5C13.5,7.4 14.4,6.5 15.5,6.5ZM8.5,6.5C9.6,6.5 10.5,7.4 10.5,8.5C10.5,9.6 9.6,10.5 8.5,10.5C7.4,10.5 6.5,9.6 6.5,8.5C6.5,7.4 7.4,6.5 8.5,6.5ZM12,7C14.8,7 17,9.2 17,12C17,14.8 14.8,17 12,17C9.2,17 7,14.8 7,12C7,9.2 9.2,7 12,7ZM12,9C10.3,9 9,10.3 9,12C9,13.7 10.3,15 12,15C13.7,15 15,13.7 15,12C15,10.3 13.7,9 12,9ZM12,18L12,22L11,22C10.4,22 10,21.6 10,21L10,19C10,18.4 10.4,18 11,18L12,18ZM13,18L14,18C14.6,18 15,18.4 15,19L15,21C15,21.6 14.6,22 14,22L13,22L13,18Z" />
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\drawable\ic_language.xml:9: Warning: Very long vector path (944 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
        android:pathData="M11.99,2C6.47,2 2,6.48 2,12s4.47,10 9.99,10C17.52,22 22,17.52 22,12S17.52,2 11.99,2zM18.92,8h-2.95c-0.32,-1.25 -0.78,-2.45 -1.38,-3.56 1.84,0.63 3.37,1.91 4.33,3.56zM12,4.04c0.83,1.2 1.48,2.53 1.91,3.96h-3.82c0.43,-1.43 1.08,-2.76 1.91,-3.96zM4.26,14C4.1,13.36 4,12.69 4,12s0.1,-1.36 0.26,-2h3.38c-0.08,0.66 -0.14,1.32 -0.14,2 0,0.68 0.06,1.34 0.14,2L4.26,14zM5.08,16h2.95c0.32,1.25 0.78,2.45 1.38,3.56C7.57,18.93 6.04,17.66 5.08,16zM8.03,8L5.08,8c0.96,-1.66 2.49,-2.93 4.33,-3.56C8.81,5.55 8.35,6.75 8.03,8zM12,19.96c-0.83,-1.2 -1.48,-2.53 -1.91,-3.96h3.82C13.48,17.43 12.83,18.76 12,19.96zM14.34,14L9.66,14c-0.09,-0.66 -0.16,-1.32 -0.16,-2 0,-0.68 0.07,-1.35 0.16,-2h4.68c0.09,0.65 0.16,1.32 0.16,2C14.5,12.68 14.43,13.34 14.34,14zM14.59,19.56c0.6,-1.11 1.06,-2.31 1.38,-3.56h2.95C18.04,17.66 16.51,18.93 14.59,19.56zM16.36,14c0.08,-0.66 0.14,-1.32 0.14,-2 0,-0.68 -0.06,-1.34 -0.14,-2h3.38c0.16,0.64 0.26,1.31 0.26,2s-0.1,1.36 -0.26,2H16.36z" />
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\drawable\ic_settings_modern.xml:9: Warning: Very long vector path (970 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
        android:pathData="M19.14,12.94C19.18,12.64 19.2,12.33 19.2,12C19.2,11.67 19.18,11.36 19.14,11.06L21.16,9.48C21.34,9.34 21.39,9.07 21.28,8.87L19.36,5.55C19.24,5.33 18.99,5.26 18.77,5.33L16.38,6.29C15.88,5.91 15.35,5.59 14.76,5.35L14.4,2.81C14.36,2.57 14.16,2.4 13.92,2.4H10.08C9.84,2.4 9.64,2.57 9.6,2.81L9.24,5.35C8.65,5.59 8.12,5.92 7.62,6.29L5.23,5.33C5.01,5.25 4.76,5.33 4.64,5.55L2.72,8.87C2.61,9.08 2.66,9.34 2.84,9.48L4.86,11.06C4.82,11.36 4.8,11.69 4.8,12C4.8,12.31 4.82,12.64 4.86,12.94L2.84,14.52C2.66,14.66 2.61,14.93 2.72,15.13L4.64,18.45C4.76,18.67 5.01,18.74 5.23,18.67L7.62,17.71C8.12,18.09 8.65,18.41 9.24,18.65L9.6,21.19C9.64,21.43 9.84,21.6 10.08,21.6H13.92C14.16,21.6 14.36,21.43 14.4,21.19L14.76,18.65C15.35,18.41 15.88,18.09 16.38,17.71L18.77,18.67C18.99,18.75 19.24,18.67 19.36,18.45L21.28,15.13C21.39,14.92 21.34,14.66 21.16,14.52L19.14,12.94ZM12,15.6C10.02,15.6 8.4,13.98 8.4,12C8.4,10.02 10.02,8.4 12,8.4C13.98,8.4 15.6,10.02 15.6,12C15.6,13.98 13.98,15.6 12,15.6Z" />
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:14: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:14: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:14: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:105: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
            <LinearLayout
             ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:312: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
            <LinearLayout
             ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:507: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
            <LinearLayout
             ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-sw600dp-land\activity_main.xml:9: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_main.xml:28: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
        <LinearLayout
         ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:24: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
        <LinearLayout
         ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:2: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
 ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation_bar.xml:2: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
 ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:27: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
        <LinearLayout
         ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:2: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
 ~~~~~~~~~~~~

   Explanation for issues of type "DisableBaselineAlignment":
   When a LinearLayout is used to distribute the space proportionally between
   nested layouts, the baseline alignment property should be turned off to
   make the layout computation faster.

Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:25: Warning: Nested weights are bad for performance [NestedWeights]
            android:layout_weight="1"
            ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:26: Warning: Nested weights are bad for performance [NestedWeights]
            android:layout_weight="1"
            ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:26: Warning: Nested weights are bad for performance [NestedWeights]
            android:layout_weight="1"
            ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:32: Warning: Nested weights are bad for performance [NestedWeights]
                android:layout_weight="1"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:33: Warning: Nested weights are bad for performance [NestedWeights]
                android:layout_weight="1"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:33: Warning: Nested weights are bad for performance [NestedWeights]
                android:layout_weight="1"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-sw600dp-land\activity_main.xml:176: Warning: Nested weights are bad for performance [NestedWeights]
            android:layout_weight="1"
            ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NestedWeights":
   Layout weights require a widget to be measured twice. When a LinearLayout
   with non-zero weights is nested inside another LinearLayout with non-zero
   weights, then the number of measurements increase exponentially.

Z:\alwan6\app\src\main\res\layout\activity_about.xml:6: Warning: Possible overdraw: Root element paints background @color/modern_background with a theme that also paints a background (inferred theme is @style/AppTheme_NoActionBar) [Overdraw]
    android:background="@color/modern_background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:8: Warning: Possible overdraw: Root element paints background @color/divider with a theme that also paints a background (inferred theme is @style/AppTheme_NoActionBar) [Overdraw]
    android:background="@color/divider"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:8: Warning: Possible overdraw: Root element paints background @color/divider with a theme that also paints a background (inferred theme is @style/AppTheme_NoActionBar) [Overdraw]
    android:background="@color/divider"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:8: Warning: Possible overdraw: Root element paints background @color/divider with a theme that also paints a background (inferred theme is @style/AppTheme_NoActionBar) [Overdraw]
    android:background="@color/divider"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:8: Warning: Possible overdraw: Root element paints background @color/modern_background with a theme that also paints a background (inferred theme is @style/AppTheme_NoActionBar) [Overdraw]
    android:background="@color/modern_background"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:6: Warning: Possible overdraw: Root element paints background @color/modern_background with a theme that also paints a background (inferred theme is @style/AppTheme_NoActionBar) [Overdraw]
    android:background="@color/modern_background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:6: Warning: Possible overdraw: Root element paints background @color/modern_background with a theme that also paints a background (inferred theme is @style/AppTheme_NoActionBar) [Overdraw]
    android:background="@color/modern_background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:6: Warning: Possible overdraw: Root element paints background @color/modern_background with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="@color/modern_background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:6: Warning: Possible overdraw: Root element paints background @color/modern_background with a theme that also paints a background (inferred theme is @style/AppTheme_NoActionBar) [Overdraw]
    android:background="@color/modern_background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:6: Warning: Possible overdraw: Root element paints background @color/modern_background with a theme that also paints a background (inferred theme is @style/AppTheme_NoActionBar) [Overdraw]
    android:background="@color/modern_background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_splash.xml:5: Warning: Possible overdraw: Root element paints background @color/colorPrimary with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="@color/colorPrimary">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:8: Warning: Possible overdraw: Root element paints background @color/card_background with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="@color/card_background"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\dialog_exit_confirmation.xml:8: Warning: Possible overdraw: Root element paints background @color/dialog_background with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="@color/dialog_background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:6: Warning: Possible overdraw: Root element paints background @color/modern_background with a theme that also paints a background (inferred theme is @style/AppTheme) [Overdraw]
    android:background="@color/modern_background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

Z:\alwan6\app\src\main\res\drawable\about.png: Warning: The resource R.drawable.about appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\layout\activity_base.xml:2: Warning: The resource R.layout.activity_base appears to be unused [UnusedResources]
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:2: Warning: The resource R.layout.activity_main_with_bottom_nav appears to be unused [UnusedResources]
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\menu\activity_nav_drawer.xml:2: Warning: The resource R.menu.activity_nav_drawer appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\layout\activity_splash.xml:2: Warning: The resource R.layout.activity_splash appears to be unused [UnusedResources]
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\raw\background_1.mp3: Warning: The resource R.raw.background_1 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\raw\background_2.mp3: Warning: The resource R.raw.background_2 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\raw\background_3.mp3: Warning: The resource R.raw.background_3 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\raw\background_4.mp3: Warning: The resource R.raw.background_4 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\raw\background_5.mp3: Warning: The resource R.raw.background_5 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\raw\background_6.mp3: Warning: The resource R.raw.background_6 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable\bottom_nav_background.xml:2: Warning: The resource R.drawable.bottom_nav_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\drawable\bottom_nav_border.xml:2: Warning: The resource R.drawable.bottom_nav_border appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\drawable\bottom_nav_item_background.xml:2: Warning: The resource R.drawable.bottom_nav_item_background appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:2: Warning: The resource R.layout.bottom_navigation appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\layout\bottom_navigation_bar.xml:2: Warning: The resource R.layout.bottom_navigation_bar appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.light appears to be unused [UnusedResources]
    <color name="light">#B2DFDB</color>
           ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.primary_text appears to be unused [UnusedResources]
    <color name="primary_text">#212121</color>
           ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.secondary_text appears to be unused [UnusedResources]
    <color name="secondary_text">#757575</color>
           ~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:34: Warning: The resource R.color.modern_primary_dark appears to be unused [UnusedResources]
    <color name="modern_primary_dark">#5a67d8</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:37: Warning: The resource R.color.modern_background_light appears to be unused [UnusedResources]
    <color name="modern_background_light">#F8F9FA</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:42: Warning: The resource R.color.nav_home_color appears to be unused [UnusedResources]
    <color name="nav_home_color">#667eea</color>
           ~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:43: Warning: The resource R.color.nav_categories_color appears to be unused [UnusedResources]
    <color name="nav_categories_color">#4ECDC4</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:67: Warning: The resource R.color.shadow_light appears to be unused [UnusedResources]
    <color name="shadow_light">#10000000</color>
           ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:68: Warning: The resource R.color.shadow_medium appears to be unused [UnusedResources]
    <color name="shadow_medium">#20000000</color>
           ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:69: Warning: The resource R.color.shadow_dark appears to be unused [UnusedResources]
    <color name="shadow_dark">#40000000</color>
           ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:77: Warning: The resource R.color.dark_background appears to be unused [UnusedResources]
    <color name="dark_background">#121212</color>
           ~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:78: Warning: The resource R.color.dark_surface appears to be unused [UnusedResources]
    <color name="dark_surface">#1E1E1E</color>
           ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:79: Warning: The resource R.color.dark_card_bg appears to be unused [UnusedResources]
    <color name="dark_card_bg">#2D2D2D</color>
           ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:80: Warning: The resource R.color.dark_text_primary appears to be unused [UnusedResources]
    <color name="dark_text_primary">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:81: Warning: The resource R.color.dark_text_secondary appears to be unused [UnusedResources]
    <color name="dark_text_secondary">#B3B3B3</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:82: Warning: The resource R.color.dark_text_on_primary appears to be unused [UnusedResources]
    <color name="dark_text_on_primary">#000000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:85: Warning: The resource R.color.dark_nav_home_color appears to be unused [UnusedResources]
    <color name="dark_nav_home_color">#8B9DC3</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:86: Warning: The resource R.color.dark_nav_categories_color appears to be unused [UnusedResources]
    <color name="dark_nav_categories_color">#6EDDD6</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:87: Warning: The resource R.color.dark_nav_gallery_color appears to be unused [UnusedResources]
    <color name="dark_nav_gallery_color">#C471CE</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:88: Warning: The resource R.color.dark_nav_favorites_color appears to be unused [UnusedResources]
    <color name="dark_nav_favorites_color">#FF8FB3</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:89: Warning: The resource R.color.dark_nav_more_color appears to be unused [UnusedResources]
    <color name="dark_nav_more_color">#9CA3AF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:92: Warning: The resource R.color.dark_flowers_color appears to be unused [UnusedResources]
    <color name="dark_flowers_color">#FF8FB3</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:93: Warning: The resource R.color.dark_cartoons_color appears to be unused [UnusedResources]
    <color name="dark_cartoons_color">#6EDDD6</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:94: Warning: The resource R.color.dark_animals_color appears to be unused [UnusedResources]
    <color name="dark_animals_color">#6BC7E8</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:95: Warning: The resource R.color.dark_foods_color appears to be unused [UnusedResources]
    <color name="dark_foods_color">#FFB74D</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:96: Warning: The resource R.color.dark_transport_color appears to be unused [UnusedResources]
    <color name="dark_transport_color">#81C784</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:97: Warning: The resource R.color.dark_nature_color appears to be unused [UnusedResources]
    <color name="dark_nature_color">#C471CE</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:100: Warning: The resource R.color.dark_modern_primary appears to be unused [UnusedResources]
    <color name="dark_modern_primary">#8B9DC3</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:101: Warning: The resource R.color.dark_modern_accent appears to be unused [UnusedResources]
    <color name="dark_modern_accent">#FFB74D</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:102: Warning: The resource R.color.dark_modern_accent_light appears to be unused [UnusedResources]
    <color name="dark_modern_accent_light">#2D2D2D</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:106: Warning: The resource R.color.surface_color appears to be unused [UnusedResources]
    <color name="surface_color">@color/modern_surface</color>
           ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:110: Warning: The resource R.color.button_background appears to be unused [UnusedResources]
    <color name="button_background">@color/modern_primary</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:111: Warning: The resource R.color.button_text appears to be unused [UnusedResources]
    <color name="button_text">@color/text_on_primary</color>
           ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:113: Warning: The resource R.color.divider_color appears to be unused [UnusedResources]
    <color name="divider_color">@color/divider</color>
           ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:114: Warning: The resource R.color.border_color appears to be unused [UnusedResources]
    <color name="border_color">#E0E0E0</color>
           ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:115: Warning: The resource R.color.error_color appears to be unused [UnusedResources]
    <color name="error_color">#F44336</color>
           ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:116: Warning: The resource R.color.warning_color appears to be unused [UnusedResources]
    <color name="warning_color">@color/modern_accent</color>
           ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\colors.xml:117: Warning: The resource R.color.success_color appears to be unused [UnusedResources]
    <color name="success_color">#4CAF50</color>
           ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\drawable\decorative_circle.xml:2: Warning: The resource R.drawable.decorative_circle appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\values\dimens.xml:3: Warning: The resource R.dimen.activity_horizontal_margin appears to be unused [UnusedResources]
    <dimen name="activity_horizontal_margin">16dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\dimens.xml:4: Warning: The resource R.dimen.activity_vertical_margin appears to be unused [UnusedResources]
    <dimen name="activity_vertical_margin">16dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\dimens.xml:7: Warning: The resource R.dimen.bottom_nav_height appears to be unused [UnusedResources]
    <dimen name="bottom_nav_height">80dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\dimens.xml:8: Warning: The resource R.dimen.ad_margin_bottom appears to be unused [UnusedResources]
    <dimen name="ad_margin_bottom">88dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:2: Warning: The resource R.layout.enhanced_bottom_navigation appears to be unused [UnusedResources]
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable-nodpi\gp1_2.png: Warning: The resource R.drawable.gp1_2 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp1_3.png: Warning: The resource R.drawable.gp1_3 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp1_4.png: Warning: The resource R.drawable.gp1_4 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp1_5.png: Warning: The resource R.drawable.gp1_5 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp1_6.png: Warning: The resource R.drawable.gp1_6 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp2_1.png: Warning: The resource R.drawable.gp2_1 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp2_2.png: Warning: The resource R.drawable.gp2_2 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp2_3.png: Warning: The resource R.drawable.gp2_3 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp2_4.png: Warning: The resource R.drawable.gp2_4 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp2_5.png: Warning: The resource R.drawable.gp2_5 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp2_6.png: Warning: The resource R.drawable.gp2_6 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp2_7.png: Warning: The resource R.drawable.gp2_7 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp2_8.png: Warning: The resource R.drawable.gp2_8 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_1.png: Warning: The resource R.drawable.gp3_1 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_10.png: Warning: The resource R.drawable.gp3_10 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_11.png: Warning: The resource R.drawable.gp3_11 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_12.png: Warning: The resource R.drawable.gp3_12 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_13.png: Warning: The resource R.drawable.gp3_13 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_14.png: Warning: The resource R.drawable.gp3_14 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_15.png: Warning: The resource R.drawable.gp3_15 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_16.png: Warning: The resource R.drawable.gp3_16 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_17.png: Warning: The resource R.drawable.gp3_17 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_18.png: Warning: The resource R.drawable.gp3_18 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_19.png: Warning: The resource R.drawable.gp3_19 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_2.png: Warning: The resource R.drawable.gp3_2 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_20.png: Warning: The resource R.drawable.gp3_20 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_21.png: Warning: The resource R.drawable.gp3_21 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_22.png: Warning: The resource R.drawable.gp3_22 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_23.png: Warning: The resource R.drawable.gp3_23 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_24.png: Warning: The resource R.drawable.gp3_24 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_25.png: Warning: The resource R.drawable.gp3_25 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_26.png: Warning: The resource R.drawable.gp3_26 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_27.png: Warning: The resource R.drawable.gp3_27 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_28.png: Warning: The resource R.drawable.gp3_28 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_29.png: Warning: The resource R.drawable.gp3_29 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_3.png: Warning: The resource R.drawable.gp3_3 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_30.png: Warning: The resource R.drawable.gp3_30 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_31.png: Warning: The resource R.drawable.gp3_31 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_32.png: Warning: The resource R.drawable.gp3_32 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_33.png: Warning: The resource R.drawable.gp3_33 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_34.png: Warning: The resource R.drawable.gp3_34 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_35.png: Warning: The resource R.drawable.gp3_35 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_36.png: Warning: The resource R.drawable.gp3_36 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_37.png: Warning: The resource R.drawable.gp3_37 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_38.png: Warning: The resource R.drawable.gp3_38 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_39.png: Warning: The resource R.drawable.gp3_39 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_4.png: Warning: The resource R.drawable.gp3_4 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_40.png: Warning: The resource R.drawable.gp3_40 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_41.png: Warning: The resource R.drawable.gp3_41 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_42.png: Warning: The resource R.drawable.gp3_42 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_43.png: Warning: The resource R.drawable.gp3_43 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_44.png: Warning: The resource R.drawable.gp3_44 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_45.png: Warning: The resource R.drawable.gp3_45 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_46.png: Warning: The resource R.drawable.gp3_46 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_47.png: Warning: The resource R.drawable.gp3_47 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_48.png: Warning: The resource R.drawable.gp3_48 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_49.png: Warning: The resource R.drawable.gp3_49 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_5.png: Warning: The resource R.drawable.gp3_5 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_50.png: Warning: The resource R.drawable.gp3_50 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_51.png: Warning: The resource R.drawable.gp3_51 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_52.png: Warning: The resource R.drawable.gp3_52 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_53.png: Warning: The resource R.drawable.gp3_53 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_54.png: Warning: The resource R.drawable.gp3_54 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_55.png: Warning: The resource R.drawable.gp3_55 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_56.png: Warning: The resource R.drawable.gp3_56 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_57.png: Warning: The resource R.drawable.gp3_57 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_58.png: Warning: The resource R.drawable.gp3_58 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_59.png: Warning: The resource R.drawable.gp3_59 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_6.png: Warning: The resource R.drawable.gp3_6 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_60.png: Warning: The resource R.drawable.gp3_60 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_61.png: Warning: The resource R.drawable.gp3_61 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_62.png: Warning: The resource R.drawable.gp3_62 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_63.png: Warning: The resource R.drawable.gp3_63 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_64.png: Warning: The resource R.drawable.gp3_64 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_65.png: Warning: The resource R.drawable.gp3_65 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_66.png: Warning: The resource R.drawable.gp3_66 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_67.png: Warning: The resource R.drawable.gp3_67 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_68.png: Warning: The resource R.drawable.gp3_68 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_69.png: Warning: The resource R.drawable.gp3_69 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_7.png: Warning: The resource R.drawable.gp3_7 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_70.png: Warning: The resource R.drawable.gp3_70 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_71.png: Warning: The resource R.drawable.gp3_71 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_72.png: Warning: The resource R.drawable.gp3_72 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_73.png: Warning: The resource R.drawable.gp3_73 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_74.png: Warning: The resource R.drawable.gp3_74 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_75.png: Warning: The resource R.drawable.gp3_75 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_76.png: Warning: The resource R.drawable.gp3_76 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_77.png: Warning: The resource R.drawable.gp3_77 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_78.png: Warning: The resource R.drawable.gp3_78 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_79.png: Warning: The resource R.drawable.gp3_79 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_8.png: Warning: The resource R.drawable.gp3_8 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_80.png: Warning: The resource R.drawable.gp3_80 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_81.png: Warning: The resource R.drawable.gp3_81 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_82.png: Warning: The resource R.drawable.gp3_82 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_83.png: Warning: The resource R.drawable.gp3_83 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_84.png: Warning: The resource R.drawable.gp3_84 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_85.png: Warning: The resource R.drawable.gp3_85 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_86.png: Warning: The resource R.drawable.gp3_86 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp3_9.png: Warning: The resource R.drawable.gp3_9 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp4_1.png: Warning: The resource R.drawable.gp4_1 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp4_2.png: Warning: The resource R.drawable.gp4_2 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp4_3.png: Warning: The resource R.drawable.gp4_3 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp4_4.png: Warning: The resource R.drawable.gp4_4 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp4_5.png: Warning: The resource R.drawable.gp4_5 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp4_6.png: Warning: The resource R.drawable.gp4_6 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp5_1.png: Warning: The resource R.drawable.gp5_1 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp5_2.png: Warning: The resource R.drawable.gp5_2 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp5_3.png: Warning: The resource R.drawable.gp5_3 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp5_4.png: Warning: The resource R.drawable.gp5_4 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp5_5.png: Warning: The resource R.drawable.gp5_5 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp5_6.png: Warning: The resource R.drawable.gp5_6 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp6_1.png: Warning: The resource R.drawable.gp6_1 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp6_2.png: Warning: The resource R.drawable.gp6_2 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp6_3.png: Warning: The resource R.drawable.gp6_3 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp6_4.png: Warning: The resource R.drawable.gp6_4 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp6_5.png: Warning: The resource R.drawable.gp6_5 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable-nodpi\gp6_6.png: Warning: The resource R.drawable.gp6_6 appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable\gradient_background.xml:2: Warning: The resource R.drawable.gradient_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_ads_modern.xml:2: Warning: The resource R.drawable.ic_ads_modern appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_app_logo.xml:1: Warning: The resource R.drawable.ic_app_logo appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_dark_mode.xml:2: Warning: The resource R.drawable.ic_dark_mode appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_delete.xml:2: Warning: The resource R.drawable.ic_delete appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_image_quality.xml:2: Warning: The resource R.drawable.ic_image_quality appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_info.xml:2: Warning: The resource R.drawable.ic_info appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_language.xml:2: Warning: The resource R.drawable.ic_language appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\mipmap-hdpi\ic_launcher.png: Warning: The resource R.mipmap.ic_launcher appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable\ic_launcher_background.xml:2: Warning: The resource R.drawable.ic_launcher_background appears to be unused [UnusedResources]
<vector
^
Z:\alwan6\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.png: Warning: The resource R.mipmap.ic_launcher_foreground appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable\ic_launcher_foreground.xml:2: Warning: The resource R.drawable.ic_launcher_foreground appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\mipmap-hdpi\ic_launcher_round.png: Warning: The resource R.mipmap.ic_launcher_round appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable\ic_notifications.xml:2: Warning: The resource R.drawable.ic_notifications appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_refresh.xml:2: Warning: The resource R.drawable.ic_refresh appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_save.xml:2: Warning: The resource R.drawable.ic_save appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_storage.xml:2: Warning: The resource R.drawable.ic_storage appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_support_modern.xml:2: Warning: The resource R.drawable.ic_support_modern appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_vibration.xml:2: Warning: The resource R.drawable.ic_vibration appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\ic_volume_up.xml:2: Warning: The resource R.drawable.ic_volume_up appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\main.png: Warning: The resource R.drawable.main appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable\modern_card_background.xml:2: Warning: The resource R.drawable.modern_card_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\drawable\modern_card_overlay.xml:2: Warning: The resource R.drawable.modern_card_overlay appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\drawable\modern_ripple_effect.xml:2: Warning: The resource R.drawable.modern_ripple_effect appears to be unused [UnusedResources]
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\nav_active_indicator.xml:2: Warning: The resource R.drawable.nav_active_indicator appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\layout\nav_header_drawer.xml:2: Warning: The resource R.layout.nav_header_drawer appears to be unused [UnusedResources]
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\nav_header_pattern.xml:2: Warning: The resource R.drawable.nav_header_pattern appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\navitemcolor.xml:2: Warning: The resource R.drawable.navitemcolor appears to be unused [UnusedResources]
    <selector xmlns:android="http://schemas.android.com/apk/res/android">
    ^
Z:\alwan6\app\src\main\res\drawable\premium_button_background.xml:2: Warning: The resource R.drawable.premium_button_background appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\drawable\premium_card_overlay.xml:2: Warning: The resource R.drawable.premium_card_overlay appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\drawable\premium_logo_background.xml:2: Warning: The resource R.drawable.premium_logo_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\menu\premium_nav_drawer.xml:2: Warning: The resource R.menu.premium_nav_drawer appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:2: Warning: The resource R.layout.premium_nav_header appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\premium_nav_header_background.xml:2: Warning: The resource R.drawable.premium_nav_header_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\drawable\premium_nav_item_background.xml:2: Warning: The resource R.drawable.premium_nav_item_background appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\drawable\premium_nav_item_color.xml:2: Warning: The resource R.drawable.premium_nav_item_color appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\drawable\premium_nav_pattern.xml:2: Warning: The resource R.drawable.premium_nav_pattern appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\premium_nav_ripple_effect.xml:2: Warning: The resource R.drawable.premium_nav_ripple_effect appears to be unused [UnusedResources]
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\premium_ripple_effect.xml:2: Warning: The resource R.drawable.premium_ripple_effect appears to be unused [UnusedResources]
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\premium_text_background.xml:2: Warning: The resource R.drawable.premium_text_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\drawable\premium_version_badge.xml:2: Warning: The resource R.drawable.premium_version_badge appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
Z:\alwan6\app\src\main\res\drawable\pressed_no_corners.xml:2: Warning: The resource R.drawable.pressed_no_corners appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android"   >
^
Z:\alwan6\app\src\main\res\drawable\privacy.png: Warning: The resource R.drawable.privacy appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable\setting_item_background.xml:2: Warning: The resource R.drawable.setting_item_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
Z:\alwan6\app\src\main\res\drawable\settings.png: Warning: The resource R.drawable.settings appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable\share.png: Warning: The resource R.drawable.share appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\drawable\star.png: Warning: The resource R.drawable.star appears to be unused [UnusedResources]
Z:\alwan6\app\src\main\res\values\strings.xml:5: Warning: The resource R.string.nav_drawer_opened appears to be unused [UnusedResources]
    <string name="nav_drawer_opened">drawer opened</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:6: Warning: The resource R.string.nav_drawer_closed appears to be unused [UnusedResources]
    <string name="nav_drawer_closed">drawer closed</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.main appears to be unused [UnusedResources]
    <string name="main">Home</string>
            ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:11: Warning: The resource R.string.about appears to be unused [UnusedResources]
    <string name="about">About</string>
            ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:12: Warning: The resource R.string.rate appears to be unused [UnusedResources]
    <string name="rate">Rate</string>
            ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:15: Warning: The resource R.string.about_text appears to be unused [UnusedResources]
    <string name="about_text">What’s New in This Version:
            ~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:53: Warning: The resource R.string.nav_categories appears to be unused [UnusedResources]
    <string name="nav_categories">Categories</string>
            ~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:59: Warning: The resource R.string.gallery appears to be unused [UnusedResources]
    <string name="gallery">Gallery</string>
            ~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:108: Warning: The resource R.string.pub_id appears to be unused [UnusedResources]
    <string name="pub_id" translatable="false">pub-7841751633097845</string>
            ~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:109: Warning: The resource R.string.appId appears to be unused [UnusedResources]
    <string name="appId" translatable="false">ca-app-pub-7841751633097845~3195890380</string>
            ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:114: Warning: The resource R.string.privacypolicy appears to be unused [UnusedResources]
    <string name="privacypolicy">Privacy Policy</string>
            ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:115: Warning: The resource R.string.soon appears to be unused [UnusedResources]
    <string name="soon">Existing the app</string>
            ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:116: Warning: The resource R.string.ads appears to be unused [UnusedResources]
    <string name="ads">Restart</string>
            ~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:117: Warning: The resource R.string.gdpr_privacypolicy appears to be unused [UnusedResources]
    <string name="gdpr_privacypolicy" tools:ignore="TypographyDashes">https://sites.google.com/view/colors-kids</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:119: Warning: The resource R.string.no_data appears to be unused [UnusedResources]
    <string name="no_data">No data</string>
            ~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:120: Warning: The resource R.string.dialog_close appears to be unused [UnusedResources]
    <string name="dialog_close">Close</string>
            ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:121: Warning: The resource R.string.google_partners appears to be unused [UnusedResources]
    <string name="google_partners">Google partner</string>
            ~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:122: Warning: The resource R.string.eu_consent_text appears to be unused [UnusedResources]
    <string name="eu_consent_text">We care about your privacy and data security. We keep this app free by showing ads.</string>
            ~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:123: Warning: The resource R.string.eu_consent_question appears to be unused [UnusedResources]
    <string name="eu_consent_question">Can we continue to use your data to tailor ads for you?</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:124: Warning: The resource R.string.eu_consent_yes appears to be unused [UnusedResources]
    <string name="eu_consent_yes">Yes, continue to see relevant ads</string>
            ~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:125: Warning: The resource R.string.eu_consent_no appears to be unused [UnusedResources]
    <string name="eu_consent_no">No, see ads that are less relevant</string>
            ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:126: Warning: The resource R.string.action_remove_ads appears to be unused [UnusedResources]
    <string name="action_remove_ads">Existing the app</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:127: Warning: The resource R.string.eu_consent_change_setting appears to be unused [UnusedResources]
    <string name="eu_consent_change_setting">You can change your choice anytime from the app settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:128: Warning: The resource R.string.learn_more appears to be unused [UnusedResources]
    <string name="learn_more">Learn how our partners collect and use data:</string>
            ~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:137: Warning: The resource R.string.settings appears to be unused [UnusedResources]
    <string name="settings">Settings</string>
            ~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:143: Warning: The resource R.string.language_settings appears to be unused [UnusedResources]
    <string name="language_settings">Language Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:148: Warning: The resource R.string.my_artworks appears to be unused [UnusedResources]
    <string name="my_artworks">My Artworks</string>
            ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:149: Warning: The resource R.string.no_saved_images appears to be unused [UnusedResources]
    <string name="no_saved_images">No saved images</string>
            ~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:150: Warning: The resource R.string.save_your_artwork appears to be unused [UnusedResources]
    <string name="save_your_artwork">Save your artwork to see it here</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:151: Warning: The resource R.string.export_gallery appears to be unused [UnusedResources]
    <string name="export_gallery">Export Gallery</string>
            ~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:154: Warning: The resource R.string.favorites appears to be unused [UnusedResources]
    <string name="favorites">Favorites</string>
            ~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:155: Warning: The resource R.string.no_favorites appears to be unused [UnusedResources]
    <string name="no_favorites">No favorites</string>
            ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:156: Warning: The resource R.string.add_favorites_message appears to be unused [UnusedResources]
    <string name="add_favorites_message">Add images to favorites to see them here</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:157: Warning: The resource R.string.export_favorites appears to be unused [UnusedResources]
    <string name="export_favorites">Export Favorites</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:160: Warning: The resource R.string.more appears to be unused [UnusedResources]
    <string name="more">More</string>
            ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:161: Warning: The resource R.string.app_features appears to be unused [UnusedResources]
    <string name="app_features">App Features</string>
            ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:207: Warning: The resource R.string.language_changed appears to be unused [UnusedResources]
    <string name="language_changed">Language changed successfully</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:211: Warning: The resource R.string.choose_quality appears to be unused [UnusedResources]
    <string name="choose_quality">Choose Quality</string>
            ~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:214: Warning: The resource R.string.error_loading appears to be unused [UnusedResources]
    <string name="error_loading">Error loading</string>
            ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:215: Warning: The resource R.string.error_saving appears to be unused [UnusedResources]
    <string name="error_saving">Error saving</string>
            ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:216: Warning: The resource R.string.error_language_change appears to be unused [UnusedResources]
    <string name="error_language_change">Error changing language</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:220: Warning: The resource R.string.title_about appears to be unused [UnusedResources]
    <string name="title_about">About</string>
            ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:221: Warning: The resource R.string.title_gallery appears to be unused [UnusedResources]
    <string name="title_gallery">Gallery</string>
            ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:222: Warning: The resource R.string.title_favorites appears to be unused [UnusedResources]
    <string name="title_favorites">Favorites</string>
            ~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:223: Warning: The resource R.string.title_more appears to be unused [UnusedResources]
    <string name="title_more">More</string>
            ~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:249: Warning: The resource R.string.app_subtitle appears to be unused [UnusedResources]
    <string name="app_subtitle">Children\'s Coloring App</string>
            ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:251: Warning: The resource R.array.Thumbnails1 appears to be unused [UnusedResources]
    <array name="Thumbnails1">
           ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:260: Warning: The resource R.array.Thumbnails2 appears to be unused [UnusedResources]
    <array name="Thumbnails2">
           ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:271: Warning: The resource R.array.Thumbnails3 appears to be unused [UnusedResources]
    <array name="Thumbnails3">
           ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:361: Warning: The resource R.array.Thumbnails4 appears to be unused [UnusedResources]
    <array name="Thumbnails4">
           ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:370: Warning: The resource R.array.Thumbnails5 appears to be unused [UnusedResources]
    <array name="Thumbnails5">
           ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\strings.xml:379: Warning: The resource R.array.Thumbnails6 appears to be unused [UnusedResources]
    <array name="Thumbnails6">
           ~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\styles.xml:19: Warning: The resource R.style.AppTheme_AppBarOverlay appears to be unused [UnusedResources]
    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values-night\styles.xml:20: Warning: The resource R.style.SettingsTheme appears to be unused [UnusedResources]
    <style name="SettingsTheme" parent="AppTheme">
           ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\styles.xml:21: Warning: The resource R.style.AppTheme_PopupOverlay appears to be unused [UnusedResources]
    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\styles.xml:23: Warning: The resource R.style.AppTheme_NoActionBar_AppBarOverlay appears to be unused [UnusedResources]
    <style name="AppTheme.NoActionBar.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\styles.xml:26: Warning: The resource R.style.PremiumNavTextAppearance appears to be unused [UnusedResources]
    <style name="PremiumNavTextAppearance" parent="TextAppearance.AppCompat.Medium">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\styles.xml:33: Warning: The resource R.style.PremiumNavItemShape appears to be unused [UnusedResources]
    <style name="PremiumNavItemShape">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values-night\styles.xml:34: Warning: The resource R.style.DarkCardStyle appears to be unused [UnusedResources]
    <style name="DarkCardStyle" parent="CardView">
           ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\styles.xml:39: Warning: The resource R.style.PremiumCardStyle appears to be unused [UnusedResources]
    <style name="PremiumCardStyle">
           ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values-night\styles.xml:41: Warning: The resource R.style.DarkButtonStyle appears to be unused [UnusedResources]
    <style name="DarkButtonStyle" parent="Widget.AppCompat.Button">
           ~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\styles.xml:47: Warning: The resource R.style.PremiumTitleText appears to be unused [UnusedResources]
    <style name="PremiumTitleText">
           ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values-night\styles.xml:49: Warning: The resource R.style.DarkTitleText appears to be unused [UnusedResources]
    <style name="DarkTitleText">
           ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values-night\styles.xml:56: Warning: The resource R.style.DarkBodyText appears to be unused [UnusedResources]
    <style name="DarkBodyText">
           ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\styles.xml:59: Warning: The resource R.style.PremiumSubtitleText appears to be unused [UnusedResources]
    <style name="PremiumSubtitleText">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values-night\styles.xml:63: Warning: The resource R.style.DarkNavigationStyle appears to be unused [UnusedResources]
    <style name="DarkNavigationStyle">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\styles.xml:67: Warning: The resource R.style.PremiumButtonStyle appears to be unused [UnusedResources]
    <style name="PremiumButtonStyle" parent="Widget.AppCompat.Button">
           ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\values\styles.xml:85: Warning: The resource R.style.SwitchTheme appears to be unused [UnusedResources]
    <style name="SwitchTheme" parent="Widget.AppCompat.CompoundButton.Switch">
           ~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:42: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:43: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:43: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:48: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:49: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:49: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:100: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:101: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:101: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:106: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:107: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:107: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:160: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:166: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:167: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:170: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:173: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:176: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:224: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:228: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:228: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:230: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:234: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:234: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:286: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:292: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:292: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:297: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:298: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:303: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:344: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:350: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:350: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:356: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:356: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:362: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <FrameLayout
                         ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_main.xml:41: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                <LinearLayout
                 ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_main.xml:137: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                <LinearLayout
                 ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_main.xml:143: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_main.xml:187: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                <LinearLayout
                 ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_main.xml:193: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                    <LinearLayout
                     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\grid_item_layout.xml:6: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\grid_item_layout.xml:7: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-sw600dp-land\grid_item_layout.xml:9: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-sw600dp-port\grid_item_layout.xml:9: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge\grid_item_layout.xml:9: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:12: Warning: This FrameLayout layout or its LinearLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
    <FrameLayout
     ~~~~~~~~~~~

   Explanation for issues of type "UselessParent":
   A layout with children that has no siblings, is not a scrollview or a root
   layout, and does not have a background, can be removed and have its
   children moved directly into the parent for a flatter and more efficient
   layout hierarchy.

Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:394: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:401: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:408: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_category_items.xml:31: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-sw600dp-land\activity_category_items.xml:31: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-sw600dp-port\activity_category_items.xml:31: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge\activity_category_items.xml:31: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_category_items.xml:32: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_category_items.xml:33: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:192: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_main.xml:196: Warning: This namespace declaration is redundant [RedundantNamespace]
            xmlns:ads="http://schemas.android.com/apk/res-auto"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:199: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-sw600dp-land\activity_main.xml:202: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_main.xml:263: Warning: This namespace declaration is redundant [RedundantNamespace]
    <com.google.android.gms.ads.AdView xmlns:ads="http://schemas.android.com/apk/res-auto"
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantNamespace":
   In Android XML documents, only specify the namespace on the root/document
   element. Namespace declarations elsewhere in the document are typically
   accidental leftovers from copy/pasting XML from other files or
   documentation.

Z:\alwan6\app\src\main\res\layout\activity_settings.xml:698: Warning: activity_settings.xml has more than 80 views, bad for performance [TooManyViews]
                        <TextView
                         ~~~~~~~~

   Explanation for issues of type "TooManyViews":
   Using too many views in a single layout is bad for performance. Consider
   using compound drawables or other tricks for reducing the number of views
   in this layout.

   The maximum view count defaults to 80 but can be configured with the
   environment variable ANDROID_LINT_MAX_VIEW_COUNT.

Z:\alwan6\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.png: Warning: The following images appear both as density independent .xml files and as bitmap files: srcmainresdrawableic_launcher_foreground.xml, srcmainresmipmap-hdpiic_launcher_foreground.png [IconXmlAndPng]

   Explanation for issues of type "IconXmlAndPng":
   If a drawable resource appears as an .xml file in the drawable/ folder,
   it's usually not intentional for it to also appear as a bitmap using the
   same name; generally you expect the drawable XML file to define states and
   each state has a corresponding drawable bitmap.

Z:\alwan6\app\src\main\res\drawable\about.png: Warning: Found bitmap drawable res/drawable/about.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\gp1.jpg: Warning: Found bitmap drawable res/drawable/gp1.jpg in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\gp2.jpg: Warning: Found bitmap drawable res/drawable/gp2.jpg in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\gp3.jpg: Warning: Found bitmap drawable res/drawable/gp3.jpg in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\gp4.jpg: Warning: Found bitmap drawable res/drawable/gp4.jpg in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\gp5.jpg: Warning: Found bitmap drawable res/drawable/gp5.jpg in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\gp6.jpg: Warning: Found bitmap drawable res/drawable/gp6.jpg in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\logo.png: Warning: Found bitmap drawable res/drawable/logo.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\main.png: Warning: Found bitmap drawable res/drawable/main.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\privacy.png: Warning: Found bitmap drawable res/drawable/privacy.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\save_icon.png: Warning: Found bitmap drawable res/drawable/save_icon.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\settings.png: Warning: Found bitmap drawable res/drawable/settings.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\share.png: Warning: Found bitmap drawable res/drawable/share.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\share_icon.png: Warning: Found bitmap drawable res/drawable/share_icon.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\small_logo.png: Warning: Found bitmap drawable res/drawable/small_logo.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\sound_off.png: Warning: Found bitmap drawable res/drawable/sound_off.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\sound_on.png: Warning: Found bitmap drawable res/drawable/sound_on.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\star.png: Warning: Found bitmap drawable res/drawable/star.png in densityless folder [IconLocation]
Z:\alwan6\app\src\main\res\drawable\undo_icon.png: Warning: Found bitmap drawable res/drawable/undo_icon.png in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:34: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:34: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:41: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:41: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:49: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:49: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:56: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:56: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:63: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:64: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:70: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:71: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:78: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:78: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:85: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:85: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:100: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:100: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:107: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:107: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:115: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:115: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:122: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:122: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:130: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:130: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:137: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:137: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:144: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:144: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:152: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:152: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:67: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                    android:textSize="10sp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:108: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                    android:textSize="10sp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:148: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                    android:textSize="10sp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:188: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                    android:textSize="10sp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:228: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                    android:textSize="10sp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:54: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:103: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:152: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation_bar.xml:60: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation_bar.xml:111: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation_bar.xml:162: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:39: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:69: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:99: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:129: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SmallSp":
   Avoid using sizes smaller than 11sp.

Z:\alwan6\app\src\main\res\menu\share_save_menu.xml:10: Warning: Prefer "ifRoom" instead of "always" [AlwaysShowAction]
        app:showAsAction="always" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "AlwaysShowAction":
   Using showAsAction="always" in menu XML, or MenuItem.SHOW_AS_ACTION_ALWAYS
   in Java code is usually a deviation from the user interface style guide.Use
   ifRoom or the corresponding MenuItem.SHOW_AS_ACTION_IF_ROOM instead.

   If always is used sparingly there are usually no problems and behavior is
   roughly equivalent to ifRoom but with preference over other ifRoom items.
   Using it more than twice in the same menu is a bad idea.

   This check looks for menu XML files that contain more than two always
   actions, or some always actions and no ifRoom actions. In Java code, it
   looks for projects that contain references to
   MenuItem.SHOW_AS_ACTION_ALWAYS and no references to
   MenuItem.SHOW_AS_ACTION_IF_ROOM.

Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:340: Warning: MainActivity#onTouch should call View#performClick when a click is detected [ClickableViewAccessibility]
    public boolean onTouch(View v, MotionEvent event) {
                   ~~~~~~~
Z:\alwan6\app\src\main\java\com\alwan\kids2025\MainActivity.java:736: Warning: Custom view MyView overrides onTouchEvent but not performClick [ClickableViewAccessibility]
        public boolean onTouchEvent(MotionEvent event) {
                       ~~~~~~~~~~~~

   Explanation for issues of type "ClickableViewAccessibility":
   If a View that overrides onTouchEvent or uses an OnTouchListener does not
   also implement performClick and call it when clicks are detected, the View
   may not handle accessibility actions properly. Logic handling the click
   actions should ideally be placed in View#performClick as some accessibility
   services invoke performClick when a click action should occur.

Z:\alwan6\app\src\main\res\layout\activity_about.xml:62: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_about.xml:172: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_about.xml:197: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_about.xml:222: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_about.xml:246: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:40: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:53: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:54: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:54: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:111: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:112: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:112: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:137: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:162: Warning: Missing contentDescription attribute on image [ContentDescription]
                                <ImageView
                                 ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:171: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:178: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:181: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:235: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:235: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:239: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:239: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:260: Warning: Missing contentDescription attribute on image [ContentDescription]
                                <ImageView
                                 ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:297: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:303: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:308: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:342: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:355: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:361: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:365: Warning: Missing contentDescription attribute on image [ContentDescription]
                                <ImageView
                                 ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:367: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:435: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:458: Warning: Missing contentDescription attribute on image [ContentDescription]
                                <ImageView
                                 ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:537: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:560: Warning: Missing contentDescription attribute on image [ContentDescription]
                                <ImageView
                                 ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:630: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_categories.xml:653: Warning: Missing contentDescription attribute on image [ContentDescription]
                                <ImageView
                                 ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:53: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:100: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:147: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:217: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:257: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_favorites.xml:294: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:53: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:100: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:147: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:210: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_gallery.xml:250: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_main.xml:182: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_main.xml:187: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main.xml:187: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-sw600dp-land\activity_main.xml:190: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_main.xml:250: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:53: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:94: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:134: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:174: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:214: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:37: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:113: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:147: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:205: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:239: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:297: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:331: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:360: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:394: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:422: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_more.xml:456: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:58: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:154: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:276: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:334: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:382: Warning: Missing contentDescription attribute on image [ContentDescription]
                            <ImageView
                             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:418: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:459: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:488: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:522: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_settings.xml:620: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_splash.xml:7: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:38: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:87: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation.xml:136: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation_bar.xml:44: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation_bar.xml:95: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\bottom_navigation_bar.xml:146: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\dialog_exit_confirmation.xml:35: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:63: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:123: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:183: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:243: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:303: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\grid_item_layout.xml:12: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\grid_item_layout.xml:13: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-sw600dp-land\grid_item_layout.xml:15: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-sw600dp-port\grid_item_layout.xml:15: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge\grid_item_layout.xml:15: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\nav_header_drawer.xml:34: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\nav_header_drawer.xml:76: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\nav_header_drawer.xml:86: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:17: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:51: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:110: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:119: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:128: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:27: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:57: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:87: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\simple_bottom_navigation.xml:117: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:65: Warning: Hardcoded string "الرئيسية", should use @string resource [HardcodedText]
                    android:text="الرئيسية"
                    ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:106: Warning: Hardcoded string "الفئات", should use @string resource [HardcodedText]
                    android:text="الفئات"
                    ~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:146: Warning: Hardcoded string "المعرض", should use @string resource [HardcodedText]
                    android:text="المعرض"
                    ~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:186: Warning: Hardcoded string "المفضلة", should use @string resource [HardcodedText]
                    android:text="المفضلة"
                    ~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\activity_main_with_bottom_nav.xml:226: Warning: Hardcoded string "المزيد", should use @string resource [HardcodedText]
                    android:text="المزيد"
                    ~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\menu\activity_nav_drawer.xml:14: Warning: Hardcoded string "التطبيق", should use @string resource [HardcodedText]
        <item android:title="التطبيق">
              ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\menu\activity_nav_drawer.xml:34: Warning: Hardcoded string "الإعدادات", should use @string resource [HardcodedText]
        <item android:title="الإعدادات">
              ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:89: Warning: Hardcoded string "Home", should use @string resource [HardcodedText]
                    android:text="Home"
                    ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:149: Warning: Hardcoded string "Categories", should use @string resource [HardcodedText]
                    android:text="Categories"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:209: Warning: Hardcoded string "Gallery", should use @string resource [HardcodedText]
                    android:text="Gallery"
                    ~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:269: Warning: Hardcoded string "Favorites", should use @string resource [HardcodedText]
                    android:text="Favorites"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\enhanced_bottom_navigation.xml:329: Warning: Hardcoded string "More", should use @string resource [HardcodedText]
                    android:text="More"
                    ~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\menu\premium_nav_drawer.xml:14: Warning: Hardcoded string "ميزات التطبيق", should use @string resource [HardcodedText]
        <item android:title="ميزات التطبيق">
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\menu\premium_nav_drawer.xml:19: Warning: Hardcoded string "فئات التلوين", should use @string resource [HardcodedText]
                    android:title="فئات التلوين" />
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\menu\premium_nav_drawer.xml:23: Warning: Hardcoded string "معرض الأعمال", should use @string resource [HardcodedText]
                    android:title="معرض الأعمال" />
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\menu\premium_nav_drawer.xml:27: Warning: Hardcoded string "المفضلة", should use @string resource [HardcodedText]
                    android:title="المفضلة" />
                    ~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\menu\premium_nav_drawer.xml:34: Warning: Hardcoded string "معلومات التطبيق", should use @string resource [HardcodedText]
        <item android:title="معلومات التطبيق">
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\menu\premium_nav_drawer.xml:54: Warning: Hardcoded string "الإعدادات والدعم", should use @string resource [HardcodedText]
        <item android:title="الإعدادات والدعم">
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\menu\premium_nav_drawer.xml:59: Warning: Hardcoded string "الإعدادات", should use @string resource [HardcodedText]
                    android:title="الإعدادات" />
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\menu\premium_nav_drawer.xml:67: Warning: Hardcoded string "الدعم الفني", should use @string resource [HardcodedText]
                    android:title="الدعم الفني" />
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:83: Warning: Hardcoded string "تطبيق التلوين العصري للأطفال", should use @string resource [HardcodedText]
                android:text="تطبيق التلوين العصري للأطفال"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout\premium_nav_header.xml:99: Warning: Hardcoded string "الإصدار 2.0 - Premium", should use @string resource [HardcodedText]
                android:text="الإصدار 2.0 - Premium"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:74: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:74: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:75: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:131: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:133: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:133: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:192: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:198: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:202: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:255: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:260: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:260: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:318: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:323: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:329: Warning: When you define paddingStart you should probably also define paddingEnd for right-to-left symmetry [RtlSymmetry]
                                    android:paddingStart="15dp"
                                    ~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlSymmetry":
   If you specify padding or margin on the left side of a layout, you should
   probably also specify padding on the right side (and vice versa) for
   right-to-left layout symmetry.

Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:64: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:65: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:65: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:122: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:123: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:123: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:182: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:189: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:192: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:246: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:250: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:250: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:308: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:314: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:319: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-port\activity_categories.xml:366: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-land\activity_categories.xml:372: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_categories.xml:378: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
                                android:layout_gravity="left|bottom"
                                                        ~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_main.xml:6: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="20dp" to better support right-to-left layouts [RtlHardcoded]
    android:layout_marginLeft="20dp"
    ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-sw600dp-land\activity_main.xml:12: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="20dp" to better support right-to-left layouts [RtlHardcoded]
        android:layout_marginLeft="20dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
Z:\alwan6\app\src\main\res\layout-xlarge-land\activity_main.xml:13: Warning: Consider replacing android:layout_marginRight with android:layout_marginEnd="20dp" to better support right-to-left layouts [RtlHardcoded]
        android:layout_marginRight="20dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlHardcoded":
   Using Gravity#LEFT and Gravity#RIGHT can lead to problems when a layout is
   rendered in locales where text flows from right to left. Use Gravity#START
   and Gravity#END instead. Similarly, in XML gravity and layout_gravity
   attributes, use start rather than left.

   For XML attributes such as paddingLeft and layout_marginLeft, use
   paddingStart and layout_marginStart. NOTE: If your minSdkVersion is less
   than 17, you should add both the older left/right attributes as well as the
   new start/end attributes. On older platforms, where RTL is not supported
   and the start/end attributes are unknown and therefore ignored, you need
   the older left/right attributes. There is a separate lint check which
   catches that type of error.

   (Note: For Gravity#LEFT and Gravity#START, you can use these constants even
   when targeting older platforms, because the start bitmask is a superset of
   the left bitmask. Therefore, you can use gravity="start" rather than
   gravity="left|start".)

74 errors, 738 warnings
