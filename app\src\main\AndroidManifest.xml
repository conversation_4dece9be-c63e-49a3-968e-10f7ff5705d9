<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <!-- Removed WRITE_EXTERNAL_STORAGE permission -->
    <!-- Removed permissions related to storage and media -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />


    <application
        android:allowBackup="false"
        android:icon="@drawable/logo"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:largeHeap="true"
        android:theme="@style/AppTheme"
        android:requestLegacyExternalStorage="true">
        <meta-data android:name="google_analytics_adid_collection_enabled" android:value="false" />
        <!-- Removed duplicate OneSignal App ID meta-data entry -->

        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-7841751633097845~3195890380"/>
        <property

            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/gma_ad_services_config"
            tools:replace="android:resource" />
        <activity android:name="com.alwan.kids2025.MainActivity"/>



        <!-- Keep Categories for backward compatibility -->
        <activity
            android:name="com.alwan.kids2025.Categories"
            android:theme="@style/AppTheme.NoActionBar"
            android:exported="false" />
        <activity
            android:name="com.alwan.kids2025.CategoryItems"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:exported="true"
            android:name="com.alwan.kids2025.Splash"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- New Activities -->
        <activity
            android:name="com.alwan.kids2025.AboutActivity"
            android:theme="@style/AppTheme.NoActionBar"
            android:parentActivityName="com.alwan.kids2025.Categories" />
        <activity
            android:name="com.alwan.kids2025.SettingsActivity"
            android:theme="@style/AppTheme.NoActionBar"
            android:parentActivityName="com.alwan.kids2025.Categories" />
        <activity
            android:name="com.alwan.kids2025.GalleryActivity"
            android:theme="@style/AppTheme.NoActionBar"
            android:parentActivityName="com.alwan.kids2025.Categories" />
        <activity
            android:name="com.alwan.kids2025.FavoritesActivity"
            android:theme="@style/AppTheme.NoActionBar"
            android:parentActivityName="com.alwan.kids2025.Categories" />
        <activity
            android:name="com.alwan.kids2025.MoreActivity"
            android:theme="@style/AppTheme.NoActionBar"
            android:parentActivityName="com.alwan.kids2025.Categories" />


        <service
            android:name="com.alwan.kids2025.MyFirebaseMessagingService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
    </application>

</manifest>


