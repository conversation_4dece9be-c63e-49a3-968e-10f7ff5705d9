package com.alwan.kids2025;

import android.app.Activity;
import android.content.Intent;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;

public class BottomNavigationHelperSimple {

    // Tab indices
    public static final int TAB_HOME = 0;
    public static final int TAB_GALLERY = 1;
    public static final int TAB_FAVORITES = 2;
    public static final int TAB_MORE = 3;

    public static void setupBottomNavigation(Activity activity, int activeTabIndex) {
        // Make sure navigation container is visible
        View navContainer = activity.findViewById(R.id.bottom_navigation_container);
        if (navContainer != null) {
            navContainer.setVisibility(View.VISIBLE);
            Log.d("BottomNavSimple", "Navigation container found and made visible");
        } else {
            Log.e("BottomNavSimple", "Navigation container NOT found! Check layout inclusion.");
        }

        // Setup click listeners
        setupClickListeners(activity);
    }

    private static void setupClickListeners(Activity activity) {
        LinearLayout navHome = activity.findViewById(R.id.nav_home);
        LinearLayout navGallery = activity.findViewById(R.id.nav_gallery);
        LinearLayout navFavorites = activity.findViewById(R.id.nav_favorites);
        LinearLayout navMore = activity.findViewById(R.id.nav_more);

        Log.d("BottomNavSimple", "Setting up click listeners - Home: " + (navHome != null) +
              ", Gallery: " + (navGallery != null) +
              ", Favorites: " + (navFavorites != null) + ", More: " + (navMore != null));

        if (navHome != null) {
            navHome.setOnClickListener(v -> {
                if (!activity.getClass().getSimpleName().equals("Categories")) {
                    Intent intent = new Intent(activity, Categories.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }

        if (navGallery != null) {
            navGallery.setOnClickListener(v -> {
                if (!activity.getClass().getSimpleName().equals("HomeActivity")) {
                    Intent intent = new Intent(activity, HomeActivity.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }

        if (navFavorites != null) {
            navFavorites.setOnClickListener(v -> {
                if (!activity.getClass().getSimpleName().equals("FavoritesActivity")) {
                    Intent intent = new Intent(activity, FavoritesActivity.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }

        if (navMore != null) {
            navMore.setOnClickListener(v -> {
                if (!activity.getClass().getSimpleName().equals("MoreActivity")) {
                    Intent intent = new Intent(activity, MoreActivity.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }
    }
}
