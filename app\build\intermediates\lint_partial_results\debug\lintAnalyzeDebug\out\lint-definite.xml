<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.1" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/GalleryActivity.java"
            line="83"
            column="26"
            startOffset="2750"
            endLine="83"
            endColumn="37"
            endOffset="2761"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/GalleryActivity.java"
            line="83"
            column="65"
            startOffset="2789"
            endLine="83"
            endColumn="76"
            endOffset="2800"/>
    </incident>

    <incident
        id="MissingFirebaseInstanceTokenRefresh"
        severity="warning"
        message="Apps that use Firebase Cloud Messaging should implement `onNewToken()` in order to observe token changes">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MyFirebaseMessagingService.java"
            line="15"
            column="14"
            startOffset="428"
            endLine="15"
            endColumn="40"
            endOffset="454"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 36"
            oldString="34"
            replacement="36"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="8"
            column="9"
            startOffset="172"
            endLine="8"
            endColumn="28"
            endOffset="191"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
            line="55"
            column="18"
            startOffset="1634"
            endLine="55"
            endColumn="31"
            endOffset="1647"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
            line="62"
            column="18"
            startOffset="1953"
            endLine="62"
            endColumn="33"
            endOffset="1968"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
            line="66"
            column="18"
            startOffset="2143"
            endLine="66"
            endColumn="30"
            endOffset="2155"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
            line="70"
            column="18"
            startOffset="2329"
            endLine="70"
            endColumn="32"
            endOffset="2343"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
            line="74"
            column="18"
            startOffset="2523"
            endLine="74"
            endColumn="31"
            endOffset="2536"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
            line="78"
            column="18"
            startOffset="2713"
            endLine="78"
            endColumn="28"
            endOffset="2723"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
            line="82"
            column="18"
            startOffset="2891"
            endLine="82"
            endColumn="28"
            endOffset="2901"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
            line="85"
            column="18"
            startOffset="2981"
            endLine="85"
            endColumn="27"
            endOffset="2990"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
            line="90"
            column="18"
            startOffset="3263"
            endLine="90"
            endColumn="30"
            endOffset="3275"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
            line="104"
            column="18"
            startOffset="4129"
            endLine="104"
            endColumn="26"
            endOffset="4137"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
            line="107"
            column="18"
            startOffset="4216"
            endLine="107"
            endColumn="36"
            endOffset="4234"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
            line="90"
            column="18"
            startOffset="2724"
            endLine="90"
            endColumn="31"
            endOffset="2737"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
            line="97"
            column="18"
            startOffset="3043"
            endLine="97"
            endColumn="33"
            endOffset="3058"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
            line="101"
            column="18"
            startOffset="3233"
            endLine="101"
            endColumn="30"
            endOffset="3245"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
            line="105"
            column="18"
            startOffset="3419"
            endLine="105"
            endColumn="32"
            endOffset="3433"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
            line="109"
            column="18"
            startOffset="3613"
            endLine="109"
            endColumn="31"
            endOffset="3626"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
            line="113"
            column="18"
            startOffset="3803"
            endLine="113"
            endColumn="28"
            endOffset="3813"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
            line="117"
            column="18"
            startOffset="3981"
            endLine="117"
            endColumn="28"
            endOffset="3991"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
            line="120"
            column="18"
            startOffset="4071"
            endLine="120"
            endColumn="27"
            endOffset="4080"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
            line="125"
            column="18"
            startOffset="4353"
            endLine="125"
            endColumn="30"
            endOffset="4365"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
            line="139"
            column="18"
            startOffset="5292"
            endLine="139"
            endColumn="26"
            endOffset="5300"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
            line="142"
            column="18"
            startOffset="5379"
            endLine="142"
            endColumn="36"
            endOffset="5397"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/Categories.java"
            line="134"
            column="18"
            startOffset="5185"
            endLine="134"
            endColumn="26"
            endOffset="5193"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/Categories.java"
            line="137"
            column="18"
            startOffset="5261"
            endLine="137"
            endColumn="26"
            endOffset="5269"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/Categories.java"
            line="140"
            column="18"
            startOffset="5337"
            endLine="140"
            endColumn="26"
            endOffset="5345"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/Categories.java"
            line="143"
            column="18"
            startOffset="5413"
            endLine="143"
            endColumn="26"
            endOffset="5421"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/Categories.java"
            line="146"
            column="18"
            startOffset="5489"
            endLine="146"
            endColumn="26"
            endOffset="5497"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/Categories.java"
            line="149"
            column="18"
            startOffset="5565"
            endLine="149"
            endColumn="26"
            endOffset="5573"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="353"
            column="18"
            startOffset="14641"
            endLine="353"
            endColumn="35"
            endOffset="14658"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="356"
            column="18"
            startOffset="14736"
            endLine="356"
            endColumn="34"
            endOffset="14752"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="359"
            column="18"
            startOffset="14824"
            endLine="359"
            endColumn="34"
            endOffset="14840"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="373"
            column="18"
            startOffset="15514"
            endLine="373"
            endColumn="34"
            endOffset="15530"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="475"
            column="18"
            startOffset="20071"
            endLine="475"
            endColumn="28"
            endOffset="20081"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="479"
            column="18"
            startOffset="20236"
            endLine="479"
            endColumn="28"
            endOffset="20246"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="483"
            column="18"
            startOffset="20401"
            endLine="483"
            endColumn="27"
            endOffset="20410"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="487"
            column="18"
            startOffset="20563"
            endLine="487"
            endColumn="35"
            endOffset="20580"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="491"
            column="18"
            startOffset="20748"
            endLine="491"
            endColumn="28"
            endOffset="20758"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="495"
            column="18"
            startOffset="20913"
            endLine="495"
            endColumn="29"
            endOffset="20924"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="499"
            column="18"
            startOffset="21081"
            endLine="499"
            endColumn="32"
            endOffset="21095"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="503"
            column="18"
            startOffset="21257"
            endLine="503"
            endColumn="33"
            endOffset="21272"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="507"
            column="18"
            startOffset="21436"
            endLine="507"
            endColumn="34"
            endOffset="21452"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="511"
            column="18"
            startOffset="21618"
            endLine="511"
            endColumn="35"
            endOffset="21635"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="515"
            column="18"
            startOffset="21803"
            endLine="515"
            endColumn="33"
            endOffset="21818"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="519"
            column="18"
            startOffset="21982"
            endLine="519"
            endColumn="34"
            endOffset="21998"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="523"
            column="18"
            startOffset="22164"
            endLine="523"
            endColumn="32"
            endOffset="22178"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="527"
            column="18"
            startOffset="22340"
            endLine="527"
            endColumn="33"
            endOffset="22355"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="531"
            column="18"
            startOffset="22519"
            endLine="531"
            endColumn="26"
            endOffset="22527"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="535"
            column="18"
            startOffset="22678"
            endLine="535"
            endColumn="34"
            endOffset="22694"/>
    </incident>

    <incident
        id="NonConstantResourceId"
        severity="warning"
        message="Resource IDs will be non-final by default in Android Gradle Plugin version 8.0, avoid using them in switch case statements">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="539"
            column="18"
            startOffset="22860"
            endLine="539"
            endColumn="35"
            endOffset="22877"/>
    </incident>

    <incident
        id="StateListReachable"
        severity="warning"
        message="This item is unreachable because a previous item (item #1) is a more general match than this one">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/pressed_no_corners.xml"
            line="10"
            column="5"
            startOffset="315"
            endLine="16"
            endColumn="12"
            endOffset="534"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/pressed_no_corners.xml"
            line="3"
            column="5"
            startOffset="118"
            endLine="9"
            endColumn="12"
            endOffset="309"
            message="Earlier item which masks item"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_nav_pattern.xml"
            line="3"
            column="20"
            startOffset="125"
            endLine="3"
            endColumn="25"
            endOffset="130"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of `compileSdkVersion` than 34 is available: 35">
        <fix-replace
            description="Set compileSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="4"
            column="5"
            startOffset="58"
            endLine="4"
            endColumn="18"
            endOffset="71"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.8.1 is available: 33.14.0">
        <fix-replace
            description="Change to 33.14.0"
            family="Update versions"
            oldString="32.8.1"
            replacement="33.14.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="50"
            column="20"
            startOffset="1828"
            endLine="50"
            endColumn="71"
            endOffset="1879"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.play:review than 2.0.1 is available: 2.0.2">
        <fix-replace
            description="Change to 2.0.2"
            family="Update versions"
            oldString="2.0.1"
            replacement="2.0.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="61"
            column="20"
            startOffset="2393"
            endLine="61"
            endColumn="58"
            endOffset="2431"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.preference:preference than 1.2.0 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.2.0"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="64"
            column="20"
            startOffset="2631"
            endLine="64"
            endColumn="58"
            endOffset="2669"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.0">
        <fix-replace
            description="Change to 1.7.0"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="66"
            column="20"
            startOffset="2721"
            endLine="66"
            endColumn="56"
            endOffset="2757"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="67"
            column="20"
            startOffset="2778"
            endLine="67"
            endColumn="51"
            endOffset="2809"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.preference:preference than 1.2.0 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.2.0"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="68"
            column="20"
            startOffset="2830"
            endLine="68"
            endColumn="58"
            endOffset="2868"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.onesignal:OneSignal than 4.8.6 is available: 4.8.7">
        <fix-replace
            description="Change to 4.8.7"
            family="Update versions"
            oldString="4.8.6"
            replacement="4.8.7"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="70"
            column="20"
            startOffset="2919"
            endLine="70"
            endColumn="51"
            endOffset="2950"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core than 1.12.0 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.12.0"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="73"
            column="20"
            startOffset="3057"
            endLine="73"
            endColumn="47"
            endOffset="3084"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity than 1.8.0 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.0"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="74"
            column="20"
            startOffset="3105"
            endLine="74"
            endColumn="54"
            endOffset="3139"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/CategoryItems.java"
            line="115"
            column="46"
            startOffset="4685"
            endLine="115"
            endColumn="59"
            endOffset="4698"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/CategoryItems.java"
            line="119"
            column="46"
            startOffset="4883"
            endLine="119"
            endColumn="59"
            endOffset="4896"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/CategoryItems.java"
            line="123"
            column="46"
            startOffset="5082"
            endLine="123"
            endColumn="59"
            endOffset="5095"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/CategoryItems.java"
            line="127"
            column="46"
            startOffset="5280"
            endLine="127"
            endColumn="59"
            endOffset="5293"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/CategoryItems.java"
            line="131"
            column="46"
            startOffset="5476"
            endLine="131"
            endColumn="59"
            endOffset="5489"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/CategoryItems.java"
            line="135"
            column="46"
            startOffset="5676"
            endLine="135"
            endColumn="59"
            endOffset="5689"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="607"
            column="60"
            startOffset="25802"
            endLine="607"
            endColumn="73"
            endOffset="25815"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="650"
            column="37"
            startOffset="27339"
            endLine="650"
            endColumn="50"
            endOffset="27352"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/flowers_color&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/flowers_color"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="177"
            column="33"
            startOffset="7783"
            endLine="177"
            endColumn="68"
            endOffset="7818"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/cartoons_color&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/cartoons_color"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="202"
            column="33"
            startOffset="9031"
            endLine="202"
            endColumn="69"
            endOffset="9067"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/animals_color&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/animals_color"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="227"
            column="33"
            startOffset="10281"
            endLine="227"
            endColumn="68"
            endOffset="10316"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/modern_accent&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/modern_accent"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="251"
            column="33"
            startOffset="11460"
            endLine="251"
            endColumn="68"
            endOffset="11495"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="167"
            column="37"
            startOffset="7529"
            endLine="167"
            endColumn="72"
            endOffset="7564"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="265"
            column="37"
            startOffset="12399"
            endLine="265"
            endColumn="72"
            endOffset="12434"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="370"
            column="37"
            startOffset="17378"
            endLine="370"
            endColumn="72"
            endOffset="17413"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="463"
            column="37"
            startOffset="21976"
            endLine="463"
            endColumn="72"
            endOffset="22011"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="565"
            column="37"
            startOffset="26898"
            endLine="565"
            endColumn="72"
            endOffset="26933"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="658"
            column="37"
            startOffset="31503"
            endLine="658"
            endColumn="72"
            endOffset="31538"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="58"
            column="25"
            startOffset="2446"
            endLine="58"
            endColumn="60"
            endOffset="2481"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="105"
            column="25"
            startOffset="4498"
            endLine="105"
            endColumn="61"
            endOffset="4534"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="152"
            column="33"
            startOffset="6914"
            endLine="152"
            endColumn="68"
            endOffset="6949"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="222"
            column="33"
            startOffset="10151"
            endLine="222"
            endColumn="69"
            endOffset="10187"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="262"
            column="33"
            startOffset="12139"
            endLine="262"
            endColumn="68"
            endOffset="12174"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/modern_accent&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/modern_accent"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="299"
            column="29"
            startOffset="13774"
            endLine="299"
            endColumn="64"
            endOffset="13809"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="58"
            column="25"
            startOffset="2442"
            endLine="58"
            endColumn="60"
            endOffset="2477"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="105"
            column="25"
            startOffset="4488"
            endLine="105"
            endColumn="61"
            endOffset="4524"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="152"
            column="33"
            startOffset="6895"
            endLine="152"
            endColumn="68"
            endOffset="6930"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="215"
            column="29"
            startOffset="9726"
            endLine="215"
            endColumn="65"
            endOffset="9762"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="255"
            column="29"
            startOffset="11558"
            endLine="255"
            endColumn="64"
            endOffset="11593"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="58"
            column="25"
            startOffset="2366"
            endLine="58"
            endColumn="60"
            endOffset="2401"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="99"
            column="25"
            startOffset="4065"
            endLine="99"
            endColumn="60"
            endOffset="4100"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="139"
            column="25"
            startOffset="5704"
            endLine="139"
            endColumn="60"
            endOffset="5739"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="179"
            column="25"
            startOffset="7350"
            endLine="179"
            endColumn="60"
            endOffset="7385"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="219"
            column="25"
            startOffset="8983"
            endLine="219"
            endColumn="60"
            endOffset="9018"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="42"
            column="25"
            startOffset="1755"
            endLine="42"
            endColumn="60"
            endOffset="1790"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="118"
            column="33"
            startOffset="5340"
            endLine="118"
            endColumn="68"
            endOffset="5375"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="151"
            column="29"
            startOffset="6953"
            endLine="151"
            endColumn="65"
            endOffset="6989"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="210"
            column="33"
            startOffset="9616"
            endLine="210"
            endColumn="68"
            endOffset="9651"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="243"
            column="29"
            startOffset="11231"
            endLine="243"
            endColumn="65"
            endOffset="11267"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="302"
            column="33"
            startOffset="13903"
            endLine="302"
            endColumn="68"
            endOffset="13938"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="335"
            column="29"
            startOffset="15518"
            endLine="335"
            endColumn="65"
            endOffset="15554"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="365"
            column="33"
            startOffset="16982"
            endLine="365"
            endColumn="68"
            endOffset="17017"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="398"
            column="29"
            startOffset="18595"
            endLine="398"
            endColumn="65"
            endOffset="18631"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="427"
            column="33"
            startOffset="20011"
            endLine="427"
            endColumn="68"
            endOffset="20046"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="460"
            column="29"
            startOffset="21636"
            endLine="460"
            endColumn="65"
            endOffset="21672"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/modern_primary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/modern_primary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="63"
            column="29"
            startOffset="2671"
            endLine="63"
            endColumn="65"
            endOffset="2707"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/cartoons_color&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/cartoons_color"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="159"
            column="29"
            startOffset="7076"
            endLine="159"
            endColumn="65"
            endOffset="7112"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/animals_color&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/animals_color"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="281"
            column="29"
            startOffset="12715"
            endLine="281"
            endColumn="64"
            endOffset="12750"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="338"
            column="33"
            startOffset="15663"
            endLine="338"
            endColumn="69"
            endOffset="15699"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="386"
            column="33"
            startOffset="18097"
            endLine="386"
            endColumn="69"
            endOffset="18133"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/nature_color&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/nature_color"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="423"
            column="29"
            startOffset="19571"
            endLine="423"
            endColumn="63"
            endOffset="19605"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="463"
            column="29"
            startOffset="21538"
            endLine="463"
            endColumn="65"
            endOffset="21574"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="492"
            column="29"
            startOffset="22918"
            endLine="492"
            endColumn="65"
            endOffset="22954"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/modern_accent&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/modern_accent"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="527"
            column="29"
            startOffset="24319"
            endLine="527"
            endColumn="64"
            endOffset="24354"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/foods_color&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/foods_color"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="625"
            column="29"
            startOffset="28759"
            endLine="625"
            endColumn="62"
            endOffset="28792"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="43"
            column="17"
            startOffset="1453"
            endLine="43"
            endColumn="52"
            endOffset="1488"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="92"
            column="17"
            startOffset="3205"
            endLine="92"
            endColumn="52"
            endOffset="3240"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="141"
            column="17"
            startOffset="4945"
            endLine="141"
            endColumn="52"
            endOffset="4980"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation_bar.xml"
            line="49"
            column="17"
            startOffset="1672"
            endLine="49"
            endColumn="52"
            endOffset="1707"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation_bar.xml"
            line="100"
            column="17"
            startOffset="3496"
            endLine="100"
            endColumn="52"
            endOffset="3531"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation_bar.xml"
            line="151"
            column="17"
            startOffset="5303"
            endLine="151"
            endColumn="52"
            endOffset="5338"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/modern_accent&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/modern_accent"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
            line="40"
            column="21"
            startOffset="1563"
            endLine="40"
            endColumn="56"
            endOffset="1598"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="68"
            column="29"
            startOffset="2824"
            endLine="68"
            endColumn="64"
            endOffset="2859"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="128"
            column="29"
            startOffset="5424"
            endLine="128"
            endColumn="64"
            endOffset="5459"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="188"
            column="29"
            startOffset="8033"
            endLine="188"
            endColumn="64"
            endOffset="8068"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="248"
            column="29"
            startOffset="10640"
            endLine="248"
            endColumn="64"
            endOffset="10675"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="308"
            column="29"
            startOffset="13230"
            endLine="308"
            endColumn="64"
            endOffset="13265"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header_drawer.xml"
            line="84"
            column="9"
            startOffset="3075"
            endLine="84"
            endColumn="44"
            endOffset="3110"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header_drawer.xml"
            line="94"
            column="9"
            startOffset="3432"
            endLine="94"
            endColumn="44"
            endOffset="3467"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="117"
            column="13"
            startOffset="4538"
            endLine="117"
            endColumn="48"
            endOffset="4573"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="126"
            column="13"
            startOffset="4868"
            endLine="126"
            endColumn="48"
            endOffset="4903"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="135"
            column="13"
            startOffset="5203"
            endLine="135"
            endColumn="48"
            endOffset="5238"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;#4CAF50&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="#4CAF50"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="31"
            column="13"
            startOffset="1104"
            endLine="31"
            endColumn="35"
            endOffset="1126"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;#AB47BC&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="#AB47BC"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="61"
            column="13"
            startOffset="2091"
            endLine="61"
            endColumn="35"
            endOffset="2113"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;#FF6B9D&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="#FF6B9D"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="91"
            column="13"
            startOffset="3087"
            endLine="91"
            endColumn="35"
            endOffset="3109"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;#6B7280&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="#6B7280"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="121"
            column="13"
            startOffset="4070"
            endLine="121"
            endColumn="35"
            endOffset="4092"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;sound_settings&quot; is not translated in &quot;ar&quot; (Arabic)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="33"
            column="13"
            startOffset="1677"
            endLine="33"
            endColumn="34"
            endOffset="1698"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;display_settings&quot; is not translated in &quot;ar&quot; (Arabic)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="34"
            column="13"
            startOffset="1736"
            endLine="34"
            endColumn="36"
            endOffset="1759"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;language_quality&quot; is not translated in &quot;ar&quot; (Arabic)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="35"
            column="13"
            startOffset="1799"
            endLine="35"
            endColumn="36"
            endOffset="1822"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;app_language&quot; is not translated in &quot;ar&quot; (Arabic)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="36"
            column="13"
            startOffset="1868"
            endLine="36"
            endColumn="32"
            endOffset="1887"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;share_text_english&quot; is not translated in &quot;ar&quot; (Arabic)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="182"
            column="13"
            startOffset="9532"
            endLine="182"
            endColumn="38"
            endOffset="9557"/>
    </incident>

    <incident
        id="ExportedService"
        severity="warning"
        message="Exported service does not require permission">
        <fix-alternatives>
            <fix-attribute
                description="Set permission"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="permission"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set exported=&quot;false&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="exported"
                value="false"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="77"
            column="10"
            startOffset="3306"
            endLine="77"
            endColumn="17"
            endOffset="3313"/>
    </incident>

    <incident
        id="DrawAllocation"
        severity="warning"
        message="Avoid object allocations during draw/layout operations (preallocate and reuse instead)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="726"
            column="38"
            startOffset="30848"
            endLine="726"
            endColumn="58"
            endOffset="30868"/>
    </incident>

    <incident
        id="Recycle"
        severity="warning"
        message="This `TypedArray` should be recycled after use with `#recycle()`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/CategoryItems.java"
            line="163"
            column="42"
            startOffset="6943"
            endLine="163"
            endColumn="58"
            endOffset="6959"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1` is never true here (`SDK_INT` = 23)">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/LanguageManager.java"
            line="98"
            column="17"
            startOffset="2999"
            endLine="98"
            endColumn="76"
            endOffset="3058"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/LanguageManager.java"
            line="152"
            column="13"
            startOffset="4664"
            endLine="152"
            endColumn="72"
            endOffset="4723"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="This folder configuration (`v21`) is unnecessary; `minSdkVersion` is 23. Merge all the resources in this folder into `drawable`.">
        <fix-data file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-v21" folderName="drawable" requiresApi="23"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-v21"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="166"
            column="26"
            startOffset="7185"
            endLine="166"
            endColumn="38"
            endOffset="7197"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="191"
            column="26"
            startOffset="8432"
            endLine="191"
            endColumn="38"
            endOffset="8444"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="216"
            column="26"
            startOffset="9683"
            endLine="216"
            endColumn="38"
            endOffset="9695"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="241"
            column="26"
            startOffset="10928"
            endLine="241"
            endColumn="38"
            endOffset="10940"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="140"
            column="26"
            startOffset="6263"
            endLine="140"
            endColumn="38"
            endOffset="6275"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="210"
            column="26"
            startOffset="9515"
            endLine="210"
            endColumn="38"
            endOffset="9527"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="250"
            column="26"
            startOffset="11502"
            endLine="250"
            endColumn="38"
            endOffset="11514"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="287"
            column="22"
            startOffset="13174"
            endLine="287"
            endColumn="34"
            endOffset="13186"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="140"
            column="26"
            startOffset="6247"
            endLine="140"
            endColumn="38"
            endOffset="6259"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="203"
            column="22"
            startOffset="9133"
            endLine="203"
            endColumn="34"
            endOffset="9145"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="243"
            column="22"
            startOffset="10966"
            endLine="243"
            endColumn="34"
            endOffset="10978"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="51"
            column="22"
            startOffset="2056"
            endLine="51"
            endColumn="34"
            endOffset="2068"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="147"
            column="22"
            startOffset="6460"
            endLine="147"
            endColumn="34"
            endOffset="6472"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="269"
            column="22"
            startOffset="12098"
            endLine="269"
            endColumn="34"
            endOffset="12110"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="411"
            column="22"
            startOffset="18955"
            endLine="411"
            endColumn="34"
            endOffset="18967"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="515"
            column="22"
            startOffset="23706"
            endLine="515"
            endColumn="34"
            endOffset="23718"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="613"
            column="22"
            startOffset="28146"
            endLine="613"
            endColumn="34"
            endOffset="28158"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1015 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_animals_modern.xml"
            line="9"
            column="27"
            startOffset="306"
            endLine="9"
            endColumn="1042"
            endOffset="1321"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (801 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_flowers_modern.xml"
            line="9"
            column="27"
            startOffset="306"
            endLine="9"
            endColumn="828"
            endOffset="1107"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (801 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_foods_modern.xml"
            line="9"
            column="27"
            startOffset="304"
            endLine="9"
            endColumn="828"
            endOffset="1105"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (944 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_language.xml"
            line="9"
            column="27"
            startOffset="306"
            endLine="9"
            endColumn="971"
            endOffset="1250"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (970 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings_modern.xml"
            line="9"
            column="27"
            startOffset="307"
            endLine="9"
            endColumn="997"
            endOffset="1277"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="14"
            column="6"
            startOffset="551"
            endLine="14"
            endColumn="18"
            endOffset="563"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="14"
            column="6"
            startOffset="551"
            endLine="14"
            endColumn="18"
            endOffset="563"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="14"
            column="6"
            startOffset="564"
            endLine="14"
            endColumn="18"
            endOffset="576"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="105"
            column="14"
            startOffset="4316"
            endLine="105"
            endColumn="26"
            endOffset="4328"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="312"
            column="14"
            startOffset="14372"
            endLine="312"
            endColumn="26"
            endOffset="14384"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="507"
            column="14"
            startOffset="23886"
            endLine="507"
            endColumn="26"
            endOffset="23898"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-sw600dp-land/activity_main.xml"
            line="9"
            column="6"
            startOffset="333"
            endLine="9"
            endColumn="18"
            endOffset="345"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_main.xml"
            line="28"
            column="10"
            startOffset="1019"
            endLine="28"
            endColumn="22"
            endOffset="1031"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="24"
            column="10"
            startOffset="903"
            endLine="24"
            endColumn="22"
            endOffset="915"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="2"
            column="2"
            startOffset="40"
            endLine="2"
            endColumn="14"
            endOffset="52"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation_bar.xml"
            line="2"
            column="2"
            startOffset="40"
            endLine="2"
            endColumn="14"
            endOffset="52"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="27"
            column="10"
            startOffset="1029"
            endLine="27"
            endColumn="22"
            endOffset="1041"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="2"
            column="2"
            startOffset="40"
            endLine="2"
            endColumn="14"
            endOffset="52"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="25"
            column="13"
            startOffset="981"
            endLine="25"
            endColumn="38"
            endOffset="1006"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="26"
            column="13"
            startOffset="958"
            endLine="26"
            endColumn="38"
            endOffset="983"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="26"
            column="13"
            startOffset="958"
            endLine="26"
            endColumn="38"
            endOffset="983"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="32"
            column="17"
            startOffset="1282"
            endLine="32"
            endColumn="42"
            endOffset="1307"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="33"
            column="17"
            startOffset="1252"
            endLine="33"
            endColumn="42"
            endOffset="1277"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="33"
            column="17"
            startOffset="1252"
            endLine="33"
            endColumn="42"
            endOffset="1277"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-sw600dp-land/activity_main.xml"
            line="176"
            column="13"
            startOffset="7251"
            endLine="176"
            endColumn="38"
            endOffset="7276"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/modern_background` with a theme that also paints a background (inferred theme is `@style/AppTheme_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="6"
            column="5"
            startOffset="292"
            endLine="6"
            endColumn="50"
            endOffset="337"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/divider` with a theme that also paints a background (inferred theme is `@style/AppTheme_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="8"
            column="5"
            startOffset="366"
            endLine="8"
            endColumn="40"
            endOffset="401"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/divider` with a theme that also paints a background (inferred theme is `@style/AppTheme_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="8"
            column="5"
            startOffset="366"
            endLine="8"
            endColumn="40"
            endOffset="401"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/divider` with a theme that also paints a background (inferred theme is `@style/AppTheme_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="8"
            column="5"
            startOffset="373"
            endLine="8"
            endColumn="40"
            endOffset="408"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/modern_background` with a theme that also paints a background (inferred theme is `@style/AppTheme_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="8"
            column="5"
            startOffset="406"
            endLine="8"
            endColumn="50"
            endOffset="451"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/modern_background` with a theme that also paints a background (inferred theme is `@style/AppTheme_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="6"
            column="5"
            startOffset="292"
            endLine="6"
            endColumn="50"
            endOffset="337"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/modern_background` with a theme that also paints a background (inferred theme is `@style/AppTheme_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="6"
            column="5"
            startOffset="292"
            endLine="6"
            endColumn="50"
            endOffset="337"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/modern_background` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="6"
            column="5"
            startOffset="292"
            endLine="6"
            endColumn="50"
            endOffset="337"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/modern_background` with a theme that also paints a background (inferred theme is `@style/AppTheme_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="6"
            column="5"
            startOffset="292"
            endLine="6"
            endColumn="50"
            endOffset="337"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/modern_background` with a theme that also paints a background (inferred theme is `@style/AppTheme_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="6"
            column="5"
            startOffset="292"
            endLine="6"
            endColumn="50"
            endOffset="337"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/colorPrimary` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml"
            line="5"
            column="5"
            startOffset="203"
            endLine="5"
            endColumn="45"
            endOffset="243"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/card_background` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="8"
            column="5"
            startOffset="321"
            endLine="8"
            endColumn="48"
            endOffset="364"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/dialog_background` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
            line="8"
            column="5"
            startOffset="309"
            endLine="8"
            endColumn="50"
            endOffset="354"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/modern_background` with a theme that also paints a background (inferred theme is `@style/AppTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="6"
            column="5"
            startOffset="292"
            endLine="6"
            endColumn="50"
            endOffset="337"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="42"
            column="22"
            startOffset="1682"
            endLine="42"
            endColumn="34"
            endOffset="1694"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="43"
            column="22"
            startOffset="1642"
            endLine="43"
            endColumn="34"
            endOffset="1654"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="43"
            column="22"
            startOffset="1642"
            endLine="43"
            endColumn="34"
            endOffset="1654"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="48"
            column="26"
            startOffset="1945"
            endLine="48"
            endColumn="37"
            endOffset="1956"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="49"
            column="26"
            startOffset="1899"
            endLine="49"
            endColumn="37"
            endOffset="1910"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="49"
            column="26"
            startOffset="1899"
            endLine="49"
            endColumn="37"
            endOffset="1910"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="100"
            column="22"
            startOffset="4430"
            endLine="100"
            endColumn="34"
            endOffset="4442"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="101"
            column="22"
            startOffset="4495"
            endLine="101"
            endColumn="34"
            endOffset="4507"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="101"
            column="22"
            startOffset="4595"
            endLine="101"
            endColumn="34"
            endOffset="4607"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="106"
            column="26"
            startOffset="4688"
            endLine="106"
            endColumn="37"
            endOffset="4699"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="107"
            column="26"
            startOffset="4753"
            endLine="107"
            endColumn="37"
            endOffset="4764"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="107"
            column="26"
            startOffset="4859"
            endLine="107"
            endColumn="37"
            endOffset="4870"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="160"
            column="22"
            startOffset="7509"
            endLine="160"
            endColumn="34"
            endOffset="7521"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="166"
            column="26"
            startOffset="7773"
            endLine="166"
            endColumn="37"
            endOffset="7784"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="167"
            column="22"
            startOffset="7570"
            endLine="167"
            endColumn="34"
            endOffset="7582"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="170"
            column="22"
            startOffset="7701"
            endLine="170"
            endColumn="34"
            endOffset="7713"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="173"
            column="26"
            startOffset="7828"
            endLine="173"
            endColumn="37"
            endOffset="7839"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="176"
            column="26"
            startOffset="7959"
            endLine="176"
            endColumn="37"
            endOffset="7970"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="224"
            column="22"
            startOffset="10360"
            endLine="224"
            endColumn="34"
            endOffset="10372"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="228"
            column="22"
            startOffset="10556"
            endLine="228"
            endColumn="34"
            endOffset="10568"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="228"
            column="22"
            startOffset="10786"
            endLine="228"
            endColumn="34"
            endOffset="10798"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="230"
            column="26"
            startOffset="10618"
            endLine="230"
            endColumn="37"
            endOffset="10629"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="234"
            column="26"
            startOffset="10814"
            endLine="234"
            endColumn="37"
            endOffset="10825"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="234"
            column="26"
            startOffset="11050"
            endLine="234"
            endColumn="37"
            endOffset="11061"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="286"
            column="22"
            startOffset="13699"
            endLine="286"
            endColumn="34"
            endOffset="13711"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="292"
            column="26"
            startOffset="13963"
            endLine="292"
            endColumn="37"
            endOffset="13974"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="292"
            column="22"
            startOffset="13505"
            endLine="292"
            endColumn="34"
            endOffset="13517"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="297"
            column="22"
            startOffset="13766"
            endLine="297"
            endColumn="34"
            endOffset="13778"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="298"
            column="26"
            startOffset="13763"
            endLine="298"
            endColumn="37"
            endOffset="13774"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="303"
            column="26"
            startOffset="14024"
            endLine="303"
            endColumn="37"
            endOffset="14035"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="344"
            column="22"
            startOffset="16609"
            endLine="344"
            endColumn="34"
            endOffset="16621"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="350"
            column="26"
            startOffset="16873"
            endLine="350"
            endColumn="37"
            endOffset="16884"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="350"
            column="22"
            startOffset="16295"
            endLine="350"
            endColumn="34"
            endOffset="16307"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="356"
            column="26"
            startOffset="16553"
            endLine="356"
            endColumn="37"
            endOffset="16564"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="356"
            column="22"
            startOffset="16621"
            endLine="356"
            endColumn="34"
            endOffset="16633"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="362"
            column="26"
            startOffset="16879"
            endLine="362"
            endColumn="37"
            endOffset="16890"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_main.xml"
            line="41"
            column="18"
            startOffset="1526"
            endLine="41"
            endColumn="30"
            endOffset="1538"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_main.xml"
            line="137"
            column="18"
            startOffset="5364"
            endLine="137"
            endColumn="30"
            endOffset="5376"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_main.xml"
            line="143"
            column="22"
            startOffset="5567"
            endLine="143"
            endColumn="34"
            endOffset="5579"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_main.xml"
            line="187"
            column="18"
            startOffset="7493"
            endLine="187"
            endColumn="30"
            endOffset="7505"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_main.xml"
            line="193"
            column="22"
            startOffset="7696"
            endLine="193"
            endColumn="34"
            endOffset="7708"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/grid_item_layout.xml"
            line="6"
            column="6"
            startOffset="194"
            endLine="6"
            endColumn="18"
            endOffset="206"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/grid_item_layout.xml"
            line="7"
            column="6"
            startOffset="190"
            endLine="7"
            endColumn="18"
            endOffset="202"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-sw600dp-land/grid_item_layout.xml"
            line="9"
            column="6"
            startOffset="349"
            endLine="9"
            endColumn="18"
            endOffset="361"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-sw600dp-port/grid_item_layout.xml"
            line="9"
            column="6"
            startOffset="349"
            endLine="9"
            endColumn="18"
            endOffset="361"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge/grid_item_layout.xml"
            line="9"
            column="6"
            startOffset="341"
            endLine="9"
            endColumn="18"
            endOffset="353"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `FrameLayout` layout or its `LinearLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="12"
            column="6"
            startOffset="449"
            endLine="12"
            endColumn="17"
            endOffset="460"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="394"
            column="9"
            startOffset="19111"
            endLine="394"
            endColumn="60"
            endOffset="19162"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="401"
            column="9"
            startOffset="18684"
            endLine="401"
            endColumn="60"
            endOffset="18735"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="408"
            column="9"
            startOffset="19075"
            endLine="408"
            endColumn="60"
            endOffset="19126"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_category_items.xml"
            line="31"
            column="9"
            startOffset="1193"
            endLine="31"
            endColumn="60"
            endOffset="1244"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-sw600dp-land/activity_category_items.xml"
            line="31"
            column="9"
            startOffset="1236"
            endLine="31"
            endColumn="60"
            endOffset="1287"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-sw600dp-port/activity_category_items.xml"
            line="31"
            column="9"
            startOffset="1236"
            endLine="31"
            endColumn="60"
            endOffset="1287"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge/activity_category_items.xml"
            line="31"
            column="9"
            startOffset="1206"
            endLine="31"
            endColumn="60"
            endOffset="1257"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_category_items.xml"
            line="32"
            column="9"
            startOffset="1245"
            endLine="32"
            endColumn="60"
            endOffset="1296"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_category_items.xml"
            line="33"
            column="9"
            startOffset="1294"
            endLine="33"
            endColumn="60"
            endOffset="1345"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="192"
            column="9"
            startOffset="7319"
            endLine="192"
            endColumn="60"
            endOffset="7370"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_main.xml"
            line="196"
            column="13"
            startOffset="7401"
            endLine="196"
            endColumn="64"
            endOffset="7452"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="199"
            column="9"
            startOffset="7435"
            endLine="199"
            endColumn="60"
            endOffset="7486"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-sw600dp-land/activity_main.xml"
            line="202"
            column="9"
            startOffset="8170"
            endLine="202"
            endColumn="60"
            endOffset="8221"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:ads"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_main.xml"
            line="263"
            column="40"
            startOffset="10589"
            endLine="263"
            endColumn="91"
            endOffset="10640"/>
    </incident>

    <incident
        id="TooManyViews"
        severity="warning"
        message="`activity_settings.xml` has more than 80 views, bad for performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="698"
            column="26"
            startOffset="32249"
            endLine="698"
            endColumn="34"
            endOffset="32257"/>
    </incident>

    <incident
        id="IconXmlAndPng"
        severity="warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\drawable\ic_launcher_foreground.xml, src\main\res\mipmap-hdpi\ic_launcher_foreground.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_foreground.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_foreground.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_foreground.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_foreground.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_foreground.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/about.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/about.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/gp1.jpg` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gp1.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/gp2.jpg` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gp2.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/gp3.jpg` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gp3.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/gp4.jpg` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gp4.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/gp5.jpg` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gp5.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/gp6.jpg` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gp6.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/logo.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logo.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/main.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/main.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/privacy.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/privacy.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/save_icon.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/save_icon.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/settings.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/settings.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/share.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/share.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/share_icon.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/share_icon.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/small_logo.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/small_logo.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/sound_off.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/sound_off.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/sound_on.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/sound_on.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/star.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/star.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/undo_icon.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/undo_icon.png"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="952"
                    endOffset="3589"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1256"
                    endOffset="1529"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1549"
                    endOffset="1820"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1842"
                    endOffset="2115"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2135"
                    endOffset="2398"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2418"
                    endOffset="2689"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2709"
                    endOffset="2982"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3004"
                    endOffset="3279"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3299"
                    endOffset="3560"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="34"
            column="18"
            startOffset="1257"
            endLine="34"
            endColumn="24"
            endOffset="1263"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="948"
                    endOffset="3585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1252"
                    endOffset="1525"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1545"
                    endOffset="1816"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1838"
                    endOffset="2111"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2131"
                    endOffset="2394"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2416"
                    endOffset="2687"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2707"
                    endOffset="2980"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3000"
                    endOffset="3275"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3295"
                    endOffset="3556"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="34"
            column="18"
            startOffset="1253"
            endLine="34"
            endColumn="24"
            endOffset="1259"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="952"
                    endOffset="3589"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1256"
                    endOffset="1529"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1549"
                    endOffset="1820"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1842"
                    endOffset="2115"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2135"
                    endOffset="2398"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2418"
                    endOffset="2689"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2709"
                    endOffset="2982"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3004"
                    endOffset="3279"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3299"
                    endOffset="3560"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="41"
            column="18"
            startOffset="1550"
            endLine="41"
            endColumn="24"
            endOffset="1556"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="948"
                    endOffset="3585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1252"
                    endOffset="1525"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1545"
                    endOffset="1816"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1838"
                    endOffset="2111"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2131"
                    endOffset="2394"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2416"
                    endOffset="2687"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2707"
                    endOffset="2980"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3000"
                    endOffset="3275"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3295"
                    endOffset="3556"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="41"
            column="18"
            startOffset="1546"
            endLine="41"
            endColumn="24"
            endOffset="1552"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="952"
                    endOffset="3589"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1256"
                    endOffset="1529"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1549"
                    endOffset="1820"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1842"
                    endOffset="2115"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2135"
                    endOffset="2398"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2418"
                    endOffset="2689"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2709"
                    endOffset="2982"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3004"
                    endOffset="3279"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3299"
                    endOffset="3560"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="49"
            column="18"
            startOffset="1843"
            endLine="49"
            endColumn="24"
            endOffset="1849"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="948"
                    endOffset="3585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1252"
                    endOffset="1525"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1545"
                    endOffset="1816"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1838"
                    endOffset="2111"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2131"
                    endOffset="2394"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2416"
                    endOffset="2687"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2707"
                    endOffset="2980"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3000"
                    endOffset="3275"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3295"
                    endOffset="3556"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="49"
            column="18"
            startOffset="1839"
            endLine="49"
            endColumn="24"
            endOffset="1845"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="952"
                    endOffset="3589"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1256"
                    endOffset="1529"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1549"
                    endOffset="1820"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1842"
                    endOffset="2115"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2135"
                    endOffset="2398"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2418"
                    endOffset="2689"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2709"
                    endOffset="2982"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3004"
                    endOffset="3279"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3299"
                    endOffset="3560"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="56"
            column="18"
            startOffset="2136"
            endLine="56"
            endColumn="24"
            endOffset="2142"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="948"
                    endOffset="3585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1252"
                    endOffset="1525"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1545"
                    endOffset="1816"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1838"
                    endOffset="2111"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2131"
                    endOffset="2394"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2416"
                    endOffset="2687"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2707"
                    endOffset="2980"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3000"
                    endOffset="3275"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3295"
                    endOffset="3556"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="56"
            column="18"
            startOffset="2132"
            endLine="56"
            endColumn="24"
            endOffset="2138"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="952"
                    endOffset="3589"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1256"
                    endOffset="1529"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1549"
                    endOffset="1820"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1842"
                    endOffset="2115"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2135"
                    endOffset="2398"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2418"
                    endOffset="2689"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2709"
                    endOffset="2982"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3004"
                    endOffset="3279"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3299"
                    endOffset="3560"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="63"
            column="18"
            startOffset="2419"
            endLine="63"
            endColumn="24"
            endOffset="2425"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="948"
                    endOffset="3585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1252"
                    endOffset="1525"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1545"
                    endOffset="1816"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1838"
                    endOffset="2111"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2131"
                    endOffset="2394"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2416"
                    endOffset="2687"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2707"
                    endOffset="2980"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3000"
                    endOffset="3275"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3295"
                    endOffset="3556"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="64"
            column="18"
            startOffset="2417"
            endLine="64"
            endColumn="24"
            endOffset="2423"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="952"
                    endOffset="3589"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1256"
                    endOffset="1529"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1549"
                    endOffset="1820"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1842"
                    endOffset="2115"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2135"
                    endOffset="2398"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2418"
                    endOffset="2689"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2709"
                    endOffset="2982"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3004"
                    endOffset="3279"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3299"
                    endOffset="3560"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="70"
            column="18"
            startOffset="2710"
            endLine="70"
            endColumn="24"
            endOffset="2716"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="948"
                    endOffset="3585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1252"
                    endOffset="1525"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1545"
                    endOffset="1816"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1838"
                    endOffset="2111"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2131"
                    endOffset="2394"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2416"
                    endOffset="2687"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2707"
                    endOffset="2980"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3000"
                    endOffset="3275"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3295"
                    endOffset="3556"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="71"
            column="18"
            startOffset="2708"
            endLine="71"
            endColumn="24"
            endOffset="2714"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="952"
                    endOffset="3589"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1256"
                    endOffset="1529"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1549"
                    endOffset="1820"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1842"
                    endOffset="2115"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2135"
                    endOffset="2398"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2418"
                    endOffset="2689"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2709"
                    endOffset="2982"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3004"
                    endOffset="3279"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3299"
                    endOffset="3560"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="78"
            column="18"
            startOffset="3005"
            endLine="78"
            endColumn="24"
            endOffset="3011"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="948"
                    endOffset="3585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1252"
                    endOffset="1525"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1545"
                    endOffset="1816"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1838"
                    endOffset="2111"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2131"
                    endOffset="2394"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2416"
                    endOffset="2687"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2707"
                    endOffset="2980"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3000"
                    endOffset="3275"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3295"
                    endOffset="3556"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="78"
            column="18"
            startOffset="3001"
            endLine="78"
            endColumn="24"
            endOffset="3007"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="952"
                    endOffset="3589"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1256"
                    endOffset="1529"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1549"
                    endOffset="1820"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="1842"
                    endOffset="2115"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2135"
                    endOffset="2398"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2418"
                    endOffset="2689"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="2709"
                    endOffset="2982"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3004"
                    endOffset="3279"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3299"
                    endOffset="3560"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="85"
            column="18"
            startOffset="3300"
            endLine="85"
            endColumn="24"
            endOffset="3306"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="948"
                    endOffset="3585"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1252"
                    endOffset="1525"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1545"
                    endOffset="1816"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="1838"
                    endOffset="2111"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2131"
                    endOffset="2394"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2416"
                    endOffset="2687"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="2707"
                    endOffset="2980"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3000"
                    endOffset="3275"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3295"
                    endOffset="3556"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="85"
            column="18"
            startOffset="3296"
            endLine="85"
            endColumn="24"
            endOffset="3302"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3605"
                    endOffset="6206"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3909"
                    endOffset="4166"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4186"
                    endOffset="4455"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4477"
                    endOffset="4748"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4768"
                    endOffset="5043"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5065"
                    endOffset="5334"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5354"
                    endOffset="5615"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5635"
                    endOffset="5894"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5916"
                    endOffset="6177"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="100"
            column="18"
            startOffset="3910"
            endLine="100"
            endColumn="24"
            endOffset="3916"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3601"
                    endOffset="6202"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3905"
                    endOffset="4162"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4182"
                    endOffset="4451"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4473"
                    endOffset="4744"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4764"
                    endOffset="5039"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5061"
                    endOffset="5330"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5350"
                    endOffset="5611"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5631"
                    endOffset="5890"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5912"
                    endOffset="6173"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="100"
            column="18"
            startOffset="3906"
            endLine="100"
            endColumn="24"
            endOffset="3912"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3605"
                    endOffset="6206"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3909"
                    endOffset="4166"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4186"
                    endOffset="4455"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4477"
                    endOffset="4748"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4768"
                    endOffset="5043"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5065"
                    endOffset="5334"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5354"
                    endOffset="5615"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5635"
                    endOffset="5894"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5916"
                    endOffset="6177"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="107"
            column="18"
            startOffset="4187"
            endLine="107"
            endColumn="24"
            endOffset="4193"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3601"
                    endOffset="6202"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3905"
                    endOffset="4162"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4182"
                    endOffset="4451"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4473"
                    endOffset="4744"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4764"
                    endOffset="5039"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5061"
                    endOffset="5330"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5350"
                    endOffset="5611"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5631"
                    endOffset="5890"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5912"
                    endOffset="6173"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="107"
            column="18"
            startOffset="4183"
            endLine="107"
            endColumn="24"
            endOffset="4189"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3605"
                    endOffset="6206"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3909"
                    endOffset="4166"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4186"
                    endOffset="4455"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4477"
                    endOffset="4748"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4768"
                    endOffset="5043"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5065"
                    endOffset="5334"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5354"
                    endOffset="5615"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5635"
                    endOffset="5894"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5916"
                    endOffset="6177"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="115"
            column="18"
            startOffset="4478"
            endLine="115"
            endColumn="24"
            endOffset="4484"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3601"
                    endOffset="6202"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3905"
                    endOffset="4162"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4182"
                    endOffset="4451"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4473"
                    endOffset="4744"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4764"
                    endOffset="5039"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5061"
                    endOffset="5330"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5350"
                    endOffset="5611"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5631"
                    endOffset="5890"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5912"
                    endOffset="6173"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="115"
            column="18"
            startOffset="4474"
            endLine="115"
            endColumn="24"
            endOffset="4480"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3605"
                    endOffset="6206"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3909"
                    endOffset="4166"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4186"
                    endOffset="4455"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4477"
                    endOffset="4748"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4768"
                    endOffset="5043"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5065"
                    endOffset="5334"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5354"
                    endOffset="5615"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5635"
                    endOffset="5894"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5916"
                    endOffset="6177"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="122"
            column="18"
            startOffset="4769"
            endLine="122"
            endColumn="24"
            endOffset="4775"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3601"
                    endOffset="6202"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3905"
                    endOffset="4162"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4182"
                    endOffset="4451"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4473"
                    endOffset="4744"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4764"
                    endOffset="5039"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5061"
                    endOffset="5330"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5350"
                    endOffset="5611"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5631"
                    endOffset="5890"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5912"
                    endOffset="6173"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="122"
            column="18"
            startOffset="4765"
            endLine="122"
            endColumn="24"
            endOffset="4771"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3605"
                    endOffset="6206"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3909"
                    endOffset="4166"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4186"
                    endOffset="4455"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4477"
                    endOffset="4748"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4768"
                    endOffset="5043"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5065"
                    endOffset="5334"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5354"
                    endOffset="5615"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5635"
                    endOffset="5894"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5916"
                    endOffset="6177"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="130"
            column="18"
            startOffset="5066"
            endLine="130"
            endColumn="24"
            endOffset="5072"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3601"
                    endOffset="6202"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3905"
                    endOffset="4162"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4182"
                    endOffset="4451"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4473"
                    endOffset="4744"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4764"
                    endOffset="5039"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5061"
                    endOffset="5330"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5350"
                    endOffset="5611"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5631"
                    endOffset="5890"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5912"
                    endOffset="6173"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="130"
            column="18"
            startOffset="5062"
            endLine="130"
            endColumn="24"
            endOffset="5068"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3605"
                    endOffset="6206"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3909"
                    endOffset="4166"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4186"
                    endOffset="4455"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4477"
                    endOffset="4748"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4768"
                    endOffset="5043"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5065"
                    endOffset="5334"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5354"
                    endOffset="5615"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5635"
                    endOffset="5894"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5916"
                    endOffset="6177"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="137"
            column="18"
            startOffset="5355"
            endLine="137"
            endColumn="24"
            endOffset="5361"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3601"
                    endOffset="6202"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3905"
                    endOffset="4162"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4182"
                    endOffset="4451"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4473"
                    endOffset="4744"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4764"
                    endOffset="5039"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5061"
                    endOffset="5330"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5350"
                    endOffset="5611"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5631"
                    endOffset="5890"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5912"
                    endOffset="6173"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="137"
            column="18"
            startOffset="5351"
            endLine="137"
            endColumn="24"
            endOffset="5357"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3605"
                    endOffset="6206"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3909"
                    endOffset="4166"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4186"
                    endOffset="4455"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4477"
                    endOffset="4748"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4768"
                    endOffset="5043"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5065"
                    endOffset="5334"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5354"
                    endOffset="5615"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5635"
                    endOffset="5894"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5916"
                    endOffset="6177"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="144"
            column="18"
            startOffset="5636"
            endLine="144"
            endColumn="24"
            endOffset="5642"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3601"
                    endOffset="6202"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3905"
                    endOffset="4162"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4182"
                    endOffset="4451"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4473"
                    endOffset="4744"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4764"
                    endOffset="5039"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5061"
                    endOffset="5330"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5350"
                    endOffset="5611"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5631"
                    endOffset="5890"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5912"
                    endOffset="6173"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="144"
            column="18"
            startOffset="5632"
            endLine="144"
            endColumn="24"
            endOffset="5638"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3605"
                    endOffset="6206"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="3909"
                    endOffset="4166"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4186"
                    endOffset="4455"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4477"
                    endOffset="4748"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="4768"
                    endOffset="5043"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5065"
                    endOffset="5334"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5354"
                    endOffset="5615"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5635"
                    endOffset="5894"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
                    startOffset="5916"
                    endOffset="6177"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="152"
            column="18"
            startOffset="5917"
            endLine="152"
            endColumn="24"
            endOffset="5923"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3601"
                    endOffset="6202"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="3905"
                    endOffset="4162"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4182"
                    endOffset="4451"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4473"
                    endOffset="4744"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="4764"
                    endOffset="5039"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5061"
                    endOffset="5330"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5350"
                    endOffset="5611"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5631"
                    endOffset="5890"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
                    startOffset="5912"
                    endOffset="6173"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="152"
            column="18"
            startOffset="5913"
            endLine="152"
            endColumn="24"
            endOffset="5919"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="67"
            column="21"
            startOffset="2725"
            endLine="67"
            endColumn="44"
            endOffset="2748"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="108"
            column="21"
            startOffset="4420"
            endLine="108"
            endColumn="44"
            endOffset="4443"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="148"
            column="21"
            startOffset="6059"
            endLine="148"
            endColumn="44"
            endOffset="6082"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="188"
            column="21"
            startOffset="7706"
            endLine="188"
            endColumn="44"
            endOffset="7729"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="228"
            column="21"
            startOffset="9338"
            endLine="228"
            endColumn="44"
            endOffset="9361"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="54"
            column="13"
            startOffset="1855"
            endLine="54"
            endColumn="36"
            endOffset="1878"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="103"
            column="13"
            startOffset="3611"
            endLine="103"
            endColumn="36"
            endOffset="3634"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="152"
            column="13"
            startOffset="5341"
            endLine="152"
            endColumn="36"
            endOffset="5364"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation_bar.xml"
            line="60"
            column="13"
            startOffset="2074"
            endLine="60"
            endColumn="36"
            endOffset="2097"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation_bar.xml"
            line="111"
            column="13"
            startOffset="3902"
            endLine="111"
            endColumn="36"
            endOffset="3925"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation_bar.xml"
            line="162"
            column="13"
            startOffset="5699"
            endLine="162"
            endColumn="36"
            endOffset="5722"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="39"
            column="13"
            startOffset="1385"
            endLine="39"
            endColumn="36"
            endOffset="1408"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="69"
            column="13"
            startOffset="2375"
            endLine="69"
            endColumn="36"
            endOffset="2398"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="99"
            column="13"
            startOffset="3373"
            endLine="99"
            endColumn="36"
            endOffset="3396"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="129"
            column="13"
            startOffset="4351"
            endLine="129"
            endColumn="36"
            endOffset="4374"/>
    </incident>

    <incident
        id="AlwaysShowAction"
        severity="warning"
        message="Prefer &quot;`ifRoom`&quot; instead of &quot;`always`&quot;">
        <fix-replace
            description="Replace with ifRoom"
            oldPattern="(always)"
            replacement="ifRoom"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/share_save_menu.xml"
            line="10"
            column="9"
            startOffset="350"
            endLine="10"
            endColumn="34"
            endOffset="375"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/share_save_menu.xml"
            line="16"
            column="9"
            startOffset="561"
            endLine="16"
            endColumn="34"
            endOffset="586"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/share_save_menu.xml"
            line="22"
            column="9"
            startOffset="771"
            endLine="22"
            endColumn="34"
            endOffset="796"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/share_save_menu.xml"
            line="28"
            column="9"
            startOffset="982"
            endLine="28"
            endColumn="34"
            endOffset="1007"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`MainActivity#onTouch` should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="340"
            column="20"
            startOffset="14291"
            endLine="340"
            endColumn="27"
            endOffset="14298"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view `MyView` overrides `onTouchEvent` but not `performClick`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="736"
            column="24"
            startOffset="31145"
            endLine="736"
            endColumn="36"
            endOffset="31157"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="62"
            column="26"
            startOffset="2621"
            endLine="62"
            endColumn="35"
            endOffset="2630"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="172"
            column="30"
            startOffset="7482"
            endLine="172"
            endColumn="39"
            endOffset="7491"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="197"
            column="30"
            startOffset="8729"
            endLine="197"
            endColumn="39"
            endOffset="8738"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="222"
            column="30"
            startOffset="9980"
            endLine="222"
            endColumn="39"
            endOffset="9989"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_about.xml"
            line="246"
            column="30"
            startOffset="11162"
            endLine="246"
            endColumn="39"
            endOffset="11171"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="40"
            column="18"
            startOffset="1641"
            endLine="40"
            endColumn="27"
            endOffset="1650"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="53"
            column="30"
            startOffset="2193"
            endLine="53"
            endColumn="39"
            endOffset="2202"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="54"
            column="30"
            startOffset="2142"
            endLine="54"
            endColumn="39"
            endOffset="2151"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="54"
            column="30"
            startOffset="2142"
            endLine="54"
            endColumn="39"
            endOffset="2151"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="111"
            column="30"
            startOffset="4931"
            endLine="111"
            endColumn="39"
            endOffset="4940"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="112"
            column="30"
            startOffset="4996"
            endLine="112"
            endColumn="39"
            endOffset="5005"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="112"
            column="30"
            startOffset="5107"
            endLine="112"
            endColumn="39"
            endOffset="5116"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="137"
            column="30"
            startOffset="5800"
            endLine="137"
            endColumn="39"
            endOffset="5809"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="162"
            column="34"
            startOffset="7190"
            endLine="162"
            endColumn="43"
            endOffset="7199"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="171"
            column="30"
            startOffset="8021"
            endLine="171"
            endColumn="39"
            endOffset="8030"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="178"
            column="30"
            startOffset="8071"
            endLine="178"
            endColumn="39"
            endOffset="8080"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="181"
            column="30"
            startOffset="8202"
            endLine="181"
            endColumn="39"
            endOffset="8211"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="235"
            column="30"
            startOffset="10861"
            endLine="235"
            endColumn="39"
            endOffset="10870"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="235"
            column="30"
            startOffset="10668"
            endLine="235"
            endColumn="39"
            endOffset="10677"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="239"
            column="30"
            startOffset="11057"
            endLine="239"
            endColumn="39"
            endOffset="11066"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="239"
            column="30"
            startOffset="11298"
            endLine="239"
            endColumn="39"
            endOffset="11307"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="260"
            column="34"
            startOffset="12059"
            endLine="260"
            endColumn="43"
            endOffset="12068"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="297"
            column="30"
            startOffset="14211"
            endLine="297"
            endColumn="39"
            endOffset="14220"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="303"
            column="30"
            startOffset="14006"
            endLine="303"
            endColumn="39"
            endOffset="14015"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="308"
            column="30"
            startOffset="14267"
            endLine="308"
            endColumn="39"
            endOffset="14276"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="342"
            column="30"
            startOffset="15765"
            endLine="342"
            endColumn="39"
            endOffset="15774"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="355"
            column="30"
            startOffset="17121"
            endLine="355"
            endColumn="39"
            endOffset="17130"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="361"
            column="30"
            startOffset="16796"
            endLine="361"
            endColumn="39"
            endOffset="16805"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="365"
            column="34"
            startOffset="17039"
            endLine="365"
            endColumn="43"
            endOffset="17048"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="367"
            column="30"
            startOffset="17122"
            endLine="367"
            endColumn="39"
            endOffset="17131"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="435"
            column="30"
            startOffset="20367"
            endLine="435"
            endColumn="39"
            endOffset="20376"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="458"
            column="34"
            startOffset="21639"
            endLine="458"
            endColumn="43"
            endOffset="21648"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="537"
            column="30"
            startOffset="25281"
            endLine="537"
            endColumn="39"
            endOffset="25290"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="560"
            column="34"
            startOffset="26557"
            endLine="560"
            endColumn="43"
            endOffset="26566"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="630"
            column="30"
            startOffset="29892"
            endLine="630"
            endColumn="39"
            endOffset="29901"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="653"
            column="34"
            startOffset="31165"
            endLine="653"
            endColumn="43"
            endOffset="31174"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="53"
            column="22"
            startOffset="2180"
            endLine="53"
            endColumn="31"
            endOffset="2189"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="100"
            column="22"
            startOffset="4235"
            endLine="100"
            endColumn="31"
            endOffset="4244"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="147"
            column="30"
            startOffset="6610"
            endLine="147"
            endColumn="39"
            endOffset="6619"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="217"
            column="30"
            startOffset="9853"
            endLine="217"
            endColumn="39"
            endOffset="9862"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="257"
            column="30"
            startOffset="11840"
            endLine="257"
            endColumn="39"
            endOffset="11849"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_favorites.xml"
            line="294"
            column="26"
            startOffset="13497"
            endLine="294"
            endColumn="35"
            endOffset="13506"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="53"
            column="22"
            startOffset="2178"
            endLine="53"
            endColumn="31"
            endOffset="2187"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="100"
            column="22"
            startOffset="4223"
            endLine="100"
            endColumn="31"
            endOffset="4232"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="147"
            column="30"
            startOffset="6594"
            endLine="147"
            endColumn="39"
            endOffset="6603"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="210"
            column="26"
            startOffset="9447"
            endLine="210"
            endColumn="35"
            endOffset="9456"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gallery.xml"
            line="250"
            column="26"
            startOffset="11280"
            endLine="250"
            endColumn="35"
            endOffset="11289"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_main.xml"
            line="182"
            column="10"
            startOffset="6929"
            endLine="182"
            endColumn="19"
            endOffset="6938"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_main.xml"
            line="187"
            column="14"
            startOffset="7051"
            endLine="187"
            endColumn="23"
            endOffset="7060"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="187"
            column="10"
            startOffset="6992"
            endLine="187"
            endColumn="19"
            endOffset="7001"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-sw600dp-land/activity_main.xml"
            line="190"
            column="18"
            startOffset="7756"
            endLine="190"
            endColumn="27"
            endOffset="7765"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_main.xml"
            line="250"
            column="22"
            startOffset="10142"
            endLine="250"
            endColumn="31"
            endOffset="10151"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="53"
            column="22"
            startOffset="2108"
            endLine="53"
            endColumn="31"
            endOffset="2117"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="94"
            column="22"
            startOffset="3801"
            endLine="94"
            endColumn="31"
            endOffset="3810"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="134"
            column="22"
            startOffset="5443"
            endLine="134"
            endColumn="31"
            endOffset="5452"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="174"
            column="22"
            startOffset="7087"
            endLine="174"
            endColumn="31"
            endOffset="7096"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="214"
            column="22"
            startOffset="8725"
            endLine="214"
            endColumn="31"
            endOffset="8734"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="37"
            column="22"
            startOffset="1494"
            endLine="37"
            endColumn="31"
            endOffset="1503"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="113"
            column="30"
            startOffset="5038"
            endLine="113"
            endColumn="39"
            endOffset="5047"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="147"
            column="26"
            startOffset="6733"
            endLine="147"
            endColumn="35"
            endOffset="6742"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="205"
            column="30"
            startOffset="9318"
            endLine="205"
            endColumn="39"
            endOffset="9327"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="239"
            column="26"
            startOffset="11011"
            endLine="239"
            endColumn="35"
            endOffset="11020"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="297"
            column="30"
            startOffset="13604"
            endLine="297"
            endColumn="39"
            endOffset="13613"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="331"
            column="26"
            startOffset="15298"
            endLine="331"
            endColumn="35"
            endOffset="15307"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="360"
            column="30"
            startOffset="16684"
            endLine="360"
            endColumn="39"
            endOffset="16693"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="394"
            column="26"
            startOffset="18375"
            endLine="394"
            endColumn="35"
            endOffset="18384"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="422"
            column="30"
            startOffset="19710"
            endLine="422"
            endColumn="39"
            endOffset="19719"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_more.xml"
            line="456"
            column="26"
            startOffset="21416"
            endLine="456"
            endColumn="35"
            endOffset="21425"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="58"
            column="26"
            startOffset="2391"
            endLine="58"
            endColumn="35"
            endOffset="2400"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="154"
            column="26"
            startOffset="6795"
            endLine="154"
            endColumn="35"
            endOffset="6804"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="276"
            column="26"
            startOffset="12433"
            endLine="276"
            endColumn="35"
            endOffset="12442"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="334"
            column="30"
            startOffset="15427"
            endLine="334"
            endColumn="39"
            endOffset="15436"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="382"
            column="30"
            startOffset="17861"
            endLine="382"
            endColumn="39"
            endOffset="17870"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="418"
            column="26"
            startOffset="19290"
            endLine="418"
            endColumn="35"
            endOffset="19299"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="459"
            column="26"
            startOffset="21318"
            endLine="459"
            endColumn="35"
            endOffset="21327"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="488"
            column="26"
            startOffset="22697"
            endLine="488"
            endColumn="35"
            endOffset="22706"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="522"
            column="26"
            startOffset="24041"
            endLine="522"
            endColumn="35"
            endOffset="24050"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="620"
            column="26"
            startOffset="28481"
            endLine="620"
            endColumn="35"
            endOffset="28490"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml"
            line="7"
            column="6"
            startOffset="253"
            endLine="7"
            endColumn="15"
            endOffset="262"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="38"
            column="14"
            startOffset="1232"
            endLine="38"
            endColumn="23"
            endOffset="1241"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="87"
            column="14"
            startOffset="2982"
            endLine="87"
            endColumn="23"
            endOffset="2991"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="136"
            column="14"
            startOffset="4727"
            endLine="136"
            endColumn="23"
            endOffset="4736"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation_bar.xml"
            line="44"
            column="14"
            startOffset="1451"
            endLine="44"
            endColumn="23"
            endOffset="1460"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation_bar.xml"
            line="95"
            column="14"
            startOffset="3273"
            endLine="95"
            endColumn="23"
            endOffset="3282"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation_bar.xml"
            line="146"
            column="14"
            startOffset="5085"
            endLine="146"
            endColumn="23"
            endOffset="5094"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
            line="35"
            column="18"
            startOffset="1328"
            endLine="35"
            endColumn="27"
            endOffset="1337"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="63"
            column="26"
            startOffset="2546"
            endLine="63"
            endColumn="35"
            endOffset="2555"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="123"
            column="26"
            startOffset="5140"
            endLine="123"
            endColumn="35"
            endOffset="5149"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="183"
            column="26"
            startOffset="7752"
            endLine="183"
            endColumn="35"
            endOffset="7761"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="243"
            column="26"
            startOffset="10357"
            endLine="243"
            endColumn="35"
            endOffset="10366"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="303"
            column="26"
            startOffset="12952"
            endLine="303"
            endColumn="35"
            endOffset="12961"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/grid_item_layout.xml"
            line="12"
            column="10"
            startOffset="395"
            endLine="12"
            endColumn="19"
            endOffset="404"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/grid_item_layout.xml"
            line="13"
            column="10"
            startOffset="385"
            endLine="13"
            endColumn="19"
            endOffset="394"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-sw600dp-land/grid_item_layout.xml"
            line="15"
            column="10"
            startOffset="550"
            endLine="15"
            endColumn="19"
            endOffset="559"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-sw600dp-port/grid_item_layout.xml"
            line="15"
            column="10"
            startOffset="550"
            endLine="15"
            endColumn="19"
            endOffset="559"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge/grid_item_layout.xml"
            line="15"
            column="10"
            startOffset="536"
            endLine="15"
            endColumn="19"
            endOffset="545"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header_drawer.xml"
            line="34"
            column="14"
            startOffset="1251"
            endLine="34"
            endColumn="23"
            endOffset="1260"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header_drawer.xml"
            line="76"
            column="6"
            startOffset="2771"
            endLine="76"
            endColumn="15"
            endOffset="2780"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header_drawer.xml"
            line="86"
            column="6"
            startOffset="3122"
            endLine="86"
            endColumn="15"
            endOffset="3131"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="17"
            column="10"
            startOffset="597"
            endLine="17"
            endColumn="19"
            endOffset="606"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="51"
            column="22"
            startOffset="1928"
            endLine="51"
            endColumn="31"
            endOffset="1937"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="110"
            column="10"
            startOffset="4263"
            endLine="110"
            endColumn="19"
            endOffset="4272"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="119"
            column="10"
            startOffset="4587"
            endLine="119"
            endColumn="19"
            endOffset="4596"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="128"
            column="10"
            startOffset="4917"
            endLine="128"
            endColumn="19"
            endOffset="4926"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="27"
            column="10"
            startOffset="950"
            endLine="27"
            endColumn="19"
            endOffset="959"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="57"
            column="10"
            startOffset="1934"
            endLine="57"
            endColumn="19"
            endOffset="1943"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="87"
            column="10"
            startOffset="2928"
            endLine="87"
            endColumn="19"
            endOffset="2937"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/simple_bottom_navigation.xml"
            line="117"
            column="10"
            startOffset="3916"
            endLine="117"
            endColumn="19"
            endOffset="3925"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;الرئيسية&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="65"
            column="21"
            startOffset="2619"
            endLine="65"
            endColumn="44"
            endOffset="2642"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;الفئات&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="106"
            column="21"
            startOffset="4318"
            endLine="106"
            endColumn="42"
            endOffset="4339"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;المعرض&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="146"
            column="21"
            startOffset="5957"
            endLine="146"
            endColumn="42"
            endOffset="5978"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;المفضلة&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="186"
            column="21"
            startOffset="7603"
            endLine="186"
            endColumn="43"
            endOffset="7625"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;المزيد&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="226"
            column="21"
            startOffset="9236"
            endLine="226"
            endColumn="42"
            endOffset="9257"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;التطبيق&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/activity_nav_drawer.xml"
            line="14"
            column="15"
            startOffset="439"
            endLine="14"
            endColumn="38"
            endOffset="462"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;الإعدادات&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/activity_nav_drawer.xml"
            line="34"
            column="15"
            startOffset="1186"
            endLine="34"
            endColumn="40"
            endOffset="1211"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="89"
            column="21"
            startOffset="3718"
            endLine="89"
            endColumn="40"
            endOffset="3737"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Categories&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="149"
            column="21"
            startOffset="6336"
            endLine="149"
            endColumn="46"
            endOffset="6361"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Gallery&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="209"
            column="21"
            startOffset="8936"
            endLine="209"
            endColumn="43"
            endOffset="8958"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Favorites&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="269"
            column="21"
            startOffset="11549"
            endLine="269"
            endColumn="45"
            endOffset="11573"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;More&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="329"
            column="21"
            startOffset="14124"
            endLine="329"
            endColumn="40"
            endOffset="14143"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;ميزات التطبيق&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/premium_nav_drawer.xml"
            line="14"
            column="15"
            startOffset="450"
            endLine="14"
            endColumn="44"
            endOffset="479"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;فئات التلوين&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/premium_nav_drawer.xml"
            line="19"
            column="21"
            startOffset="657"
            endLine="19"
            endColumn="49"
            endOffset="685"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;معرض الأعمال&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/premium_nav_drawer.xml"
            line="23"
            column="21"
            startOffset="840"
            endLine="23"
            endColumn="49"
            endOffset="868"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;المفضلة&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/premium_nav_drawer.xml"
            line="27"
            column="21"
            startOffset="1027"
            endLine="27"
            endColumn="44"
            endOffset="1050"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;معلومات التطبيق&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/premium_nav_drawer.xml"
            line="34"
            column="15"
            startOffset="1200"
            endLine="34"
            endColumn="46"
            endOffset="1231"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;الإعدادات والدعم&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/premium_nav_drawer.xml"
            line="54"
            column="15"
            startOffset="1945"
            endLine="54"
            endColumn="47"
            endOffset="1977"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;الإعدادات&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/premium_nav_drawer.xml"
            line="59"
            column="21"
            startOffset="2151"
            endLine="59"
            endColumn="46"
            endOffset="2176"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;الدعم الفني&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/premium_nav_drawer.xml"
            line="67"
            column="21"
            startOffset="2529"
            endLine="67"
            endColumn="48"
            endOffset="2556"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;تطبيق التلوين العصري للأطفال&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="83"
            column="17"
            startOffset="3196"
            endLine="83"
            endColumn="60"
            endOffset="3239"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;الإصدار 2.0 - Premium&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="99"
            column="17"
            startOffset="3859"
            endLine="99"
            endColumn="53"
            endOffset="3895"/>
    </incident>

</incidents>
