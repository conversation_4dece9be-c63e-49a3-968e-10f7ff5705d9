<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.1" type="partial_results">
    <map id="AppBundleLocaleChanges">
        <location id="localeChangeLocation"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/LanguageManager.java"
            line="94"
            column="13"
            startOffset="2836"
            endLine="94"
            endColumn="44"
            endOffset="2867"/>
    </map>
    <map id="NotificationPermission">
        <location id="class"
            file="$GRADLE_USER_HOME/caches/8.11.1/transforms/45fdc981b26c02b1afc1001d148fb2a9/transformed/jetified-OneSignal-4.8.6/jars/classes.jar"/>
        <entry
            name="className"
            string="com/onesignal/GenerateNotification"/>
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.intent.action.VIEW (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
                            line="87"
                            column="28"
                            startOffset="3075"
                            endLine="87"
                            endColumn="134"
                            endOffset="3181"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
                            line="100"
                            column="45"
                            startOffset="3926"
                            endLine="100"
                            endColumn="131"
                            endOffset="4012"/>
                        <location id="2"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseActivity.java"
                            line="108"
                            column="33"
                            startOffset="4268"
                            endLine="108"
                            endColumn="119"
                            endOffset="4354"/>
                        <location id="3"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
                            line="122"
                            column="28"
                            startOffset="4165"
                            endLine="122"
                            endColumn="134"
                            endOffset="4271"/>
                        <location id="4"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
                            line="135"
                            column="45"
                            startOffset="5089"
                            endLine="135"
                            endColumn="131"
                            endOffset="5175"/>
                        <location id="5"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/BaseLocalizedActivity.java"
                            line="143"
                            column="33"
                            startOffset="5431"
                            endLine="143"
                            endColumn="119"
                            endOffset="5517"/>
                        <location id="6"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MoreActivity.java"
                            line="56"
                            column="29"
                            startOffset="1674"
                            endLine="57"
                            endColumn="91"
                            endOffset="1795"/>
                        <location id="7"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MoreActivity.java"
                            line="62"
                            column="29"
                            startOffset="1919"
                            endLine="63"
                            endColumn="72"
                            endOffset="2021"/>
                        <location id="8"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/SettingsActivity.java"
                            line="186"
                            column="29"
                            startOffset="8400"
                            endLine="187"
                            endColumn="72"
                            endOffset="8502"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="incidents">
                    <map id="0">
                        <entry
                            name="incidentClass"
                            string="com.alwan.kids2025.LanguageManager"/>
                        <location id="location"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/LanguageManager.java"
                            line="131"
                            column="25"
                            startOffset="4086"
                            endLine="131"
                            endColumn="45"
                            endOffset="4106"/>
                        <location id="secondaryLocation"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/LanguageManager.java"
                            line="133"
                            column="9"
                            startOffset="4143"
                            endLine="133"
                            endColumn="39"
                            endOffset="4173"
                            message="The unsafe intent is launched here."/>
                    </map>
            </map>
            <map id="unprotected">
                <entry
                    name="com.alwan.kids2025.MyFirebaseMessagingService"
                    boolean="true"/>
                <entry
                    name="com.alwan.kids2025.Splash"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.array.Thumbnails1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="251"
            column="12"
            startOffset="13621"
            endLine="251"
            endColumn="30"
            endOffset="13639"/>
        <location id="R.array.Thumbnails2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="260"
            column="12"
            startOffset="13897"
            endLine="260"
            endColumn="30"
            endOffset="13915"/>
        <location id="R.array.Thumbnails3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="271"
            column="12"
            startOffset="14249"
            endLine="271"
            endColumn="30"
            endOffset="14267"/>
        <location id="R.array.Thumbnails4"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="361"
            column="12"
            startOffset="17644"
            endLine="361"
            endColumn="30"
            endOffset="17662"/>
        <location id="R.array.Thumbnails5"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="370"
            column="12"
            startOffset="17920"
            endLine="370"
            endColumn="30"
            endOffset="17938"/>
        <location id="R.array.Thumbnails6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="379"
            column="12"
            startOffset="18196"
            endLine="379"
            endColumn="30"
            endOffset="18214"/>
        <location id="R.color.border_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="114"
            column="12"
            startOffset="4900"
            endLine="114"
            endColumn="31"
            endOffset="4919"/>
        <location id="R.color.button_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="110"
            column="12"
            startOffset="4665"
            endLine="110"
            endColumn="36"
            endOffset="4689"/>
        <location id="R.color.button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="111"
            column="12"
            startOffset="4732"
            endLine="111"
            endColumn="30"
            endOffset="4750"/>
        <location id="R.color.dark_animals_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="94"
            column="12"
            startOffset="3846"
            endLine="94"
            endColumn="37"
            endOffset="3871"/>
        <location id="R.color.dark_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="77"
            column="12"
            startOffset="3061"
            endLine="77"
            endColumn="34"
            endOffset="3083"/>
        <location id="R.color.dark_card_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="79"
            column="12"
            startOffset="3160"
            endLine="79"
            endColumn="31"
            endOffset="3179"/>
        <location id="R.color.dark_cartoons_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="93"
            column="12"
            startOffset="3791"
            endLine="93"
            endColumn="38"
            endOffset="3817"/>
        <location id="R.color.dark_flowers_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="92"
            column="12"
            startOffset="3737"
            endLine="92"
            endColumn="37"
            endOffset="3762"/>
        <location id="R.color.dark_foods_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="95"
            column="12"
            startOffset="3900"
            endLine="95"
            endColumn="35"
            endOffset="3923"/>
        <location id="R.color.dark_modern_accent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="101"
            column="12"
            startOffset="4151"
            endLine="101"
            endColumn="37"
            endOffset="4176"/>
        <location id="R.color.dark_modern_accent_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="102"
            column="12"
            startOffset="4205"
            endLine="102"
            endColumn="43"
            endOffset="4236"/>
        <location id="R.color.dark_modern_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="100"
            column="12"
            startOffset="4096"
            endLine="100"
            endColumn="38"
            endOffset="4122"/>
        <location id="R.color.dark_nature_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="97"
            column="12"
            startOffset="4008"
            endLine="97"
            endColumn="36"
            endOffset="4032"/>
        <location id="R.color.dark_nav_categories_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="86"
            column="12"
            startOffset="3466"
            endLine="86"
            endColumn="44"
            endOffset="3498"/>
        <location id="R.color.dark_nav_favorites_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="88"
            column="12"
            startOffset="3585"
            endLine="88"
            endColumn="43"
            endOffset="3616"/>
        <location id="R.color.dark_nav_gallery_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="87"
            column="12"
            startOffset="3527"
            endLine="87"
            endColumn="41"
            endOffset="3556"/>
        <location id="R.color.dark_nav_home_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="85"
            column="12"
            startOffset="3411"
            endLine="85"
            endColumn="38"
            endOffset="3437"/>
        <location id="R.color.dark_nav_more_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="89"
            column="12"
            startOffset="3645"
            endLine="89"
            endColumn="38"
            endOffset="3671"/>
        <location id="R.color.dark_surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="78"
            column="12"
            startOffset="3112"
            endLine="78"
            endColumn="31"
            endOffset="3131"/>
        <location id="R.color.dark_text_on_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="82"
            column="12"
            startOffset="3316"
            endLine="82"
            endColumn="39"
            endOffset="3343"/>
        <location id="R.color.dark_text_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="80"
            column="12"
            startOffset="3208"
            endLine="80"
            endColumn="36"
            endOffset="3232"/>
        <location id="R.color.dark_text_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="81"
            column="12"
            startOffset="3261"
            endLine="81"
            endColumn="38"
            endOffset="3287"/>
        <location id="R.color.dark_transport_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="96"
            column="12"
            startOffset="3952"
            endLine="96"
            endColumn="39"
            endOffset="3979"/>
        <location id="R.color.divider_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="113"
            column="12"
            startOffset="4844"
            endLine="113"
            endColumn="32"
            endOffset="4864"/>
        <location id="R.color.error_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="115"
            column="12"
            startOffset="4948"
            endLine="115"
            endColumn="30"
            endOffset="4966"/>
        <location id="R.color.light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="211"
            endLine="6"
            endColumn="24"
            endOffset="223"/>
        <location id="R.color.modern_background_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="37"
            column="12"
            startOffset="1508"
            endLine="37"
            endColumn="42"
            endOffset="1538"/>
        <location id="R.color.modern_primary_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="34"
            column="12"
            startOffset="1351"
            endLine="34"
            endColumn="38"
            endOffset="1377"/>
        <location id="R.color.nav_categories_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="43"
            column="12"
            startOffset="1751"
            endLine="43"
            endColumn="39"
            endOffset="1778"/>
        <location id="R.color.nav_home_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="42"
            column="12"
            startOffset="1701"
            endLine="42"
            endColumn="33"
            endOffset="1722"/>
        <location id="R.color.primary_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="252"
            endLine="7"
            endColumn="31"
            endOffset="271"/>
        <location id="R.color.secondary_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="300"
            endLine="8"
            endColumn="33"
            endOffset="321"/>
        <location id="R.color.shadow_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="69"
            column="12"
            startOffset="2780"
            endLine="69"
            endColumn="30"
            endOffset="2798"/>
        <location id="R.color.shadow_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="67"
            column="12"
            startOffset="2679"
            endLine="67"
            endColumn="31"
            endOffset="2698"/>
        <location id="R.color.shadow_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="68"
            column="12"
            startOffset="2729"
            endLine="68"
            endColumn="32"
            endOffset="2749"/>
        <location id="R.color.success_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="117"
            column="12"
            startOffset="5057"
            endLine="117"
            endColumn="32"
            endOffset="5077"/>
        <location id="R.color.surface_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="106"
            column="12"
            startOffset="4396"
            endLine="106"
            endColumn="32"
            endOffset="4416"/>
        <location id="R.color.warning_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="116"
            column="12"
            startOffset="4995"
            endLine="116"
            endColumn="32"
            endOffset="5015"/>
        <location id="R.dimen.activity_horizontal_margin"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="3"
            column="12"
            startOffset="97"
            endLine="3"
            endColumn="45"
            endOffset="130"/>
        <location id="R.dimen.activity_vertical_margin"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="4"
            column="12"
            startOffset="156"
            endLine="4"
            endColumn="43"
            endOffset="187"/>
        <location id="R.dimen.ad_margin_bottom"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="8"
            column="12"
            startOffset="301"
            endLine="8"
            endColumn="35"
            endOffset="324"/>
        <location id="R.dimen.bottom_nav_height"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="7"
            column="12"
            startOffset="251"
            endLine="7"
            endColumn="36"
            endOffset="275"/>
        <location id="R.drawable.about"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/about.png"/>
        <location id="R.drawable.bottom_nav_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bottom_nav_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="9"
            endOffset="391"/>
        <location id="R.drawable.bottom_nav_border"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bottom_nav_border.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="8"
            endColumn="9"
            endOffset="266"/>
        <location id="R.drawable.bottom_nav_item_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bottom_nav_item_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="27"
            endColumn="12"
            endOffset="976"/>
        <location id="R.drawable.decorative_circle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/decorative_circle.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="311"/>
        <location id="R.drawable.gp1_2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp1_2.png"/>
        <location id="R.drawable.gp1_3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp1_3.png"/>
        <location id="R.drawable.gp1_4"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp1_4.png"/>
        <location id="R.drawable.gp1_5"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp1_5.png"/>
        <location id="R.drawable.gp1_6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp1_6.png"/>
        <location id="R.drawable.gp2_1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp2_1.png"/>
        <location id="R.drawable.gp2_2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp2_2.png"/>
        <location id="R.drawable.gp2_3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp2_3.png"/>
        <location id="R.drawable.gp2_4"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp2_4.png"/>
        <location id="R.drawable.gp2_5"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp2_5.png"/>
        <location id="R.drawable.gp2_6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp2_6.png"/>
        <location id="R.drawable.gp2_7"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp2_7.png"/>
        <location id="R.drawable.gp2_8"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp2_8.png"/>
        <location id="R.drawable.gp3_1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_1.png"/>
        <location id="R.drawable.gp3_10"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_10.png"/>
        <location id="R.drawable.gp3_11"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_11.png"/>
        <location id="R.drawable.gp3_12"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_12.png"/>
        <location id="R.drawable.gp3_13"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_13.png"/>
        <location id="R.drawable.gp3_14"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_14.png"/>
        <location id="R.drawable.gp3_15"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_15.png"/>
        <location id="R.drawable.gp3_16"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_16.png"/>
        <location id="R.drawable.gp3_17"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_17.png"/>
        <location id="R.drawable.gp3_18"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_18.png"/>
        <location id="R.drawable.gp3_19"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_19.png"/>
        <location id="R.drawable.gp3_2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_2.png"/>
        <location id="R.drawable.gp3_20"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_20.png"/>
        <location id="R.drawable.gp3_21"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_21.png"/>
        <location id="R.drawable.gp3_22"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_22.png"/>
        <location id="R.drawable.gp3_23"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_23.png"/>
        <location id="R.drawable.gp3_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_24.png"/>
        <location id="R.drawable.gp3_25"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_25.png"/>
        <location id="R.drawable.gp3_26"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_26.png"/>
        <location id="R.drawable.gp3_27"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_27.png"/>
        <location id="R.drawable.gp3_28"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_28.png"/>
        <location id="R.drawable.gp3_29"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_29.png"/>
        <location id="R.drawable.gp3_3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_3.png"/>
        <location id="R.drawable.gp3_30"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_30.png"/>
        <location id="R.drawable.gp3_31"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_31.png"/>
        <location id="R.drawable.gp3_32"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_32.png"/>
        <location id="R.drawable.gp3_33"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_33.png"/>
        <location id="R.drawable.gp3_34"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_34.png"/>
        <location id="R.drawable.gp3_35"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_35.png"/>
        <location id="R.drawable.gp3_36"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_36.png"/>
        <location id="R.drawable.gp3_37"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_37.png"/>
        <location id="R.drawable.gp3_38"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_38.png"/>
        <location id="R.drawable.gp3_39"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_39.png"/>
        <location id="R.drawable.gp3_4"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_4.png"/>
        <location id="R.drawable.gp3_40"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_40.png"/>
        <location id="R.drawable.gp3_41"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_41.png"/>
        <location id="R.drawable.gp3_42"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_42.png"/>
        <location id="R.drawable.gp3_43"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_43.png"/>
        <location id="R.drawable.gp3_44"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_44.png"/>
        <location id="R.drawable.gp3_45"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_45.png"/>
        <location id="R.drawable.gp3_46"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_46.png"/>
        <location id="R.drawable.gp3_47"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_47.png"/>
        <location id="R.drawable.gp3_48"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_48.png"/>
        <location id="R.drawable.gp3_49"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_49.png"/>
        <location id="R.drawable.gp3_5"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_5.png"/>
        <location id="R.drawable.gp3_50"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_50.png"/>
        <location id="R.drawable.gp3_51"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_51.png"/>
        <location id="R.drawable.gp3_52"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_52.png"/>
        <location id="R.drawable.gp3_53"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_53.png"/>
        <location id="R.drawable.gp3_54"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_54.png"/>
        <location id="R.drawable.gp3_55"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_55.png"/>
        <location id="R.drawable.gp3_56"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_56.png"/>
        <location id="R.drawable.gp3_57"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_57.png"/>
        <location id="R.drawable.gp3_58"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_58.png"/>
        <location id="R.drawable.gp3_59"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_59.png"/>
        <location id="R.drawable.gp3_6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_6.png"/>
        <location id="R.drawable.gp3_60"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_60.png"/>
        <location id="R.drawable.gp3_61"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_61.png"/>
        <location id="R.drawable.gp3_62"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_62.png"/>
        <location id="R.drawable.gp3_63"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_63.png"/>
        <location id="R.drawable.gp3_64"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_64.png"/>
        <location id="R.drawable.gp3_65"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_65.png"/>
        <location id="R.drawable.gp3_66"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_66.png"/>
        <location id="R.drawable.gp3_67"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_67.png"/>
        <location id="R.drawable.gp3_68"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_68.png"/>
        <location id="R.drawable.gp3_69"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_69.png"/>
        <location id="R.drawable.gp3_7"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_7.png"/>
        <location id="R.drawable.gp3_70"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_70.png"/>
        <location id="R.drawable.gp3_71"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_71.png"/>
        <location id="R.drawable.gp3_72"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_72.png"/>
        <location id="R.drawable.gp3_73"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_73.png"/>
        <location id="R.drawable.gp3_74"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_74.png"/>
        <location id="R.drawable.gp3_75"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_75.png"/>
        <location id="R.drawable.gp3_76"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_76.png"/>
        <location id="R.drawable.gp3_77"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_77.png"/>
        <location id="R.drawable.gp3_78"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_78.png"/>
        <location id="R.drawable.gp3_79"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_79.png"/>
        <location id="R.drawable.gp3_8"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_8.png"/>
        <location id="R.drawable.gp3_80"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_80.png"/>
        <location id="R.drawable.gp3_81"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_81.png"/>
        <location id="R.drawable.gp3_82"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_82.png"/>
        <location id="R.drawable.gp3_83"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_83.png"/>
        <location id="R.drawable.gp3_84"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_84.png"/>
        <location id="R.drawable.gp3_85"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_85.png"/>
        <location id="R.drawable.gp3_86"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_86.png"/>
        <location id="R.drawable.gp3_9"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp3_9.png"/>
        <location id="R.drawable.gp4_1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp4_1.png"/>
        <location id="R.drawable.gp4_2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp4_2.png"/>
        <location id="R.drawable.gp4_3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp4_3.png"/>
        <location id="R.drawable.gp4_4"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp4_4.png"/>
        <location id="R.drawable.gp4_5"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp4_5.png"/>
        <location id="R.drawable.gp4_6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp4_6.png"/>
        <location id="R.drawable.gp5_1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp5_1.png"/>
        <location id="R.drawable.gp5_2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp5_2.png"/>
        <location id="R.drawable.gp5_3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp5_3.png"/>
        <location id="R.drawable.gp5_4"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp5_4.png"/>
        <location id="R.drawable.gp5_5"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp5_5.png"/>
        <location id="R.drawable.gp5_6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp5_6.png"/>
        <location id="R.drawable.gp6_1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp6_1.png"/>
        <location id="R.drawable.gp6_2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp6_2.png"/>
        <location id="R.drawable.gp6_3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp6_3.png"/>
        <location id="R.drawable.gp6_4"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp6_4.png"/>
        <location id="R.drawable.gp6_5"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp6_5.png"/>
        <location id="R.drawable.gp6_6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-nodpi/gp6_6.png"/>
        <location id="R.drawable.gradient_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="9"
            endOffset="344"/>
        <location id="R.drawable.ic_ads_modern"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_ads_modern.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="798"/>
        <location id="R.drawable.ic_app_logo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_app_logo.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="420"/>
        <location id="R.drawable.ic_dark_mode"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_dark_mode.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="463"/>
        <location id="R.drawable.ic_delete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_delete.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="403"/>
        <location id="R.drawable.ic_image_quality"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image_quality.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="455"/>
        <location id="R.drawable.ic_info"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_info.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="420"/>
        <location id="R.drawable.ic_language"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_language.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="1264"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="74"
            endColumn="10"
            endOffset="4866"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="338"/>
        <location id="R.drawable.ic_notifications"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notifications.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="504"/>
        <location id="R.drawable.ic_refresh"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_refresh.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="538"/>
        <location id="R.drawable.ic_save"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_save.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="488"/>
        <location id="R.drawable.ic_storage"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_storage.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="409"/>
        <location id="R.drawable.ic_support_modern"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_support_modern.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="671"/>
        <location id="R.drawable.ic_vibration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_vibration.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="494"/>
        <location id="R.drawable.ic_volume_up"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_volume_up.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="527"/>
        <location id="R.drawable.main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/main.png"/>
        <location id="R.drawable.modern_card_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/modern_card_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="8"
            endColumn="9"
            endOffset="279"/>
        <location id="R.drawable.modern_card_overlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/modern_card_overlay.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="9"
            endOffset="342"/>
        <location id="R.drawable.modern_ripple_effect"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/modern_ripple_effect.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="375"/>
        <location id="R.drawable.nav_active_indicator"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/nav_active_indicator.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="12"
            endColumn="9"
            endOffset="365"/>
        <location id="R.drawable.nav_header_pattern"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/nav_header_pattern.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="37"
            endColumn="10"
            endOffset="1311"/>
        <location id="R.drawable.navitemcolor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/navitemcolor.xml"
            line="2"
            column="5"
            startOffset="44"
            endLine="5"
            endColumn="16"
            endOffset="269"/>
        <location id="R.drawable.premium_button_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_button_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="24"
            endColumn="12"
            endOffset="879"/>
        <location id="R.drawable.premium_card_overlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_card_overlay.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="9"
            endOffset="342"/>
        <location id="R.drawable.premium_logo_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_logo_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="12"
            endColumn="9"
            endOffset="417"/>
        <location id="R.drawable.premium_nav_header_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_nav_header_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="340"/>
        <location id="R.drawable.premium_nav_item_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_nav_item_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="21"
            endColumn="12"
            endOffset="746"/>
        <location id="R.drawable.premium_nav_item_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_nav_item_color.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="12"
            endOffset="329"/>
        <location id="R.drawable.premium_nav_pattern"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_nav_pattern.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="67"
            endColumn="10"
            endOffset="2538"/>
        <location id="R.drawable.premium_nav_ripple_effect"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_nav_ripple_effect.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="22"
            endColumn="10"
            endOffset="694"/>
        <location id="R.drawable.premium_ripple_effect"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_ripple_effect.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="20"
            endColumn="10"
            endOffset="717"/>
        <location id="R.drawable.premium_text_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_text_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="9"
            endOffset="362"/>
        <location id="R.drawable.premium_version_badge"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/premium_version_badge.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="8"
            endColumn="9"
            endOffset="269"/>
        <location id="R.drawable.pressed_no_corners"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/pressed_no_corners.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="25"
            endColumn="12"
            endOffset="772"/>
        <location id="R.drawable.privacy"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/privacy.png"/>
        <location id="R.drawable.setting_item_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/setting_item_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="9"
            endOffset="346"/>
        <location id="R.drawable.settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/settings.png"/>
        <location id="R.drawable.share"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/share.png"/>
        <location id="R.drawable.star"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/star.png"/>
        <location id="R.layout.activity_base"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_base.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="52"
            endColumn="45"
            endOffset="2255"/>
        <location id="R.layout.activity_main_with_bottom_nav"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_with_bottom_nav.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="237"
            endColumn="55"
            endOffset="9572"/>
        <location id="R.layout.activity_splash"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="14"
            endColumn="18"
            endOffset="511"/>
        <location id="R.layout.bottom_navigation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="165"
            endColumn="16"
            endOffset="5736"/>
        <location id="R.layout.bottom_navigation_bar"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/bottom_navigation_bar.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="176"
            endColumn="16"
            endOffset="6131"/>
        <location id="R.layout.enhanced_bottom_navigation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/enhanced_bottom_navigation.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="341"
            endColumn="55"
            endOffset="14503"/>
        <location id="R.layout.nav_header_drawer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header_drawer.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="96"
            endColumn="18"
            endOffset="3491"/>
        <location id="R.layout.premium_nav_header"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/premium_nav_header.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="139"
            endColumn="16"
            endOffset="5278"/>
        <location id="R.menu.activity_nav_drawer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/activity_nav_drawer.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="48"
            endColumn="8"
            endOffset="1680"/>
        <location id="R.menu.premium_nav_drawer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/premium_nav_drawer.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="76"
            endColumn="8"
            endOffset="2791"/>
        <location id="R.mipmap.ic_launcher"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.png"/>
        <location id="R.mipmap.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_foreground.png"/>
        <location id="R.mipmap.ic_launcher_round"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.png"/>
        <location id="R.raw.background_1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/background_1.mp3"/>
        <location id="R.raw.background_2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/background_2.mp3"/>
        <location id="R.raw.background_3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/background_3.mp3"/>
        <location id="R.raw.background_4"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/background_4.mp3"/>
        <location id="R.raw.background_5"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/background_5.mp3"/>
        <location id="R.raw.background_6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/background_6.mp3"/>
        <location id="R.string.about"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="11"
            column="13"
            startOffset="551"
            endLine="11"
            endColumn="25"
            endOffset="563"/>
        <location id="R.string.about_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="709"
            endLine="15"
            endColumn="30"
            endOffset="726"/>
        <location id="R.string.action_remove_ads"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="126"
            column="13"
            startOffset="6750"
            endLine="126"
            endColumn="37"
            endOffset="6774"/>
        <location id="R.string.add_favorites_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="156"
            column="13"
            startOffset="8130"
            endLine="156"
            endColumn="41"
            endOffset="8158"/>
        <location id="R.string.ads"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="116"
            column="13"
            startOffset="6042"
            endLine="116"
            endColumn="23"
            endOffset="6052"/>
        <location id="R.string.appId"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="109"
            column="13"
            startOffset="5513"
            endLine="109"
            endColumn="25"
            endOffset="5525"/>
        <location id="R.string.app_features"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="161"
            column="13"
            startOffset="8352"
            endLine="161"
            endColumn="32"
            endOffset="8371"/>
        <location id="R.string.app_subtitle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="249"
            column="13"
            startOffset="13553"
            endLine="249"
            endColumn="32"
            endOffset="13572"/>
        <location id="R.string.choose_quality"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="211"
            column="13"
            startOffset="11651"
            endLine="211"
            endColumn="34"
            endOffset="11672"/>
        <location id="R.string.dialog_close"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="120"
            column="13"
            startOffset="6252"
            endLine="120"
            endColumn="32"
            endOffset="6271"/>
        <location id="R.string.error_language_change"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="216"
            column="13"
            startOffset="11853"
            endLine="216"
            endColumn="41"
            endOffset="11881"/>
        <location id="R.string.error_loading"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="214"
            column="13"
            startOffset="11741"
            endLine="214"
            endColumn="33"
            endOffset="11761"/>
        <location id="R.string.error_saving"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="215"
            column="13"
            startOffset="11798"
            endLine="215"
            endColumn="32"
            endOffset="11817"/>
        <location id="R.string.eu_consent_change_setting"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="127"
            column="13"
            startOffset="6814"
            endLine="127"
            endColumn="45"
            endOffset="6846"/>
        <location id="R.string.eu_consent_no"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="125"
            column="13"
            startOffset="6672"
            endLine="125"
            endColumn="33"
            endOffset="6692"/>
        <location id="R.string.eu_consent_question"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="123"
            column="13"
            startOffset="6489"
            endLine="123"
            endColumn="39"
            endOffset="6515"/>
        <location id="R.string.eu_consent_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="122"
            column="13"
            startOffset="6360"
            endLine="122"
            endColumn="35"
            endOffset="6382"/>
        <location id="R.string.eu_consent_yes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="124"
            column="13"
            startOffset="6594"
            endLine="124"
            endColumn="34"
            endOffset="6615"/>
        <location id="R.string.export_favorites"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="157"
            column="13"
            startOffset="8222"
            endLine="157"
            endColumn="36"
            endOffset="8245"/>
        <location id="R.string.export_gallery"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="151"
            column="13"
            startOffset="7941"
            endLine="151"
            endColumn="34"
            endOffset="7962"/>
        <location id="R.string.favorites"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="154"
            column="13"
            startOffset="8026"
            endLine="154"
            endColumn="29"
            endOffset="8042"/>
        <location id="R.string.gallery"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="59"
            column="13"
            startOffset="2899"
            endLine="59"
            endColumn="27"
            endOffset="2913"/>
        <location id="R.string.gdpr_privacypolicy"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="117"
            column="13"
            startOffset="6083"
            endLine="117"
            endColumn="38"
            endOffset="6108"/>
        <location id="R.string.google_partners"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="121"
            column="13"
            startOffset="6300"
            endLine="121"
            endColumn="35"
            endOffset="6322"/>
        <location id="R.string.language_changed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="207"
            column="13"
            startOffset="11484"
            endLine="207"
            endColumn="36"
            endOffset="11507"/>
        <location id="R.string.language_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="143"
            column="13"
            startOffset="7654"
            endLine="143"
            endColumn="37"
            endOffset="7678"/>
        <location id="R.string.learn_more"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="128"
            column="13"
            startOffset="6926"
            endLine="128"
            endColumn="30"
            endOffset="6943"/>
        <location id="R.string.main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="512"
            endLine="10"
            endColumn="24"
            endOffset="523"/>
        <location id="R.string.more"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="160"
            column="13"
            startOffset="8313"
            endLine="160"
            endColumn="24"
            endOffset="8324"/>
        <location id="R.string.my_artworks"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="148"
            column="13"
            startOffset="7747"
            endLine="148"
            endColumn="31"
            endOffset="7765"/>
        <location id="R.string.nav_categories"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="53"
            column="13"
            startOffset="2663"
            endLine="53"
            endColumn="34"
            endOffset="2684"/>
        <location id="R.string.nav_drawer_closed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="276"
            endLine="6"
            endColumn="37"
            endOffset="300"/>
        <location id="R.string.nav_drawer_opened"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="215"
            endLine="5"
            endColumn="37"
            endOffset="239"/>
        <location id="R.string.no_data"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="119"
            column="13"
            startOffset="6207"
            endLine="119"
            endColumn="27"
            endOffset="6221"/>
        <location id="R.string.no_favorites"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="155"
            column="13"
            startOffset="8075"
            endLine="155"
            endColumn="32"
            endOffset="8094"/>
        <location id="R.string.no_saved_images"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="149"
            column="13"
            startOffset="7800"
            endLine="149"
            endColumn="35"
            endOffset="7822"/>
        <location id="R.string.privacypolicy"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="114"
            column="13"
            startOffset="5933"
            endLine="114"
            endColumn="33"
            endOffset="5953"/>
        <location id="R.string.pub_id"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="108"
            column="13"
            startOffset="5435"
            endLine="108"
            endColumn="26"
            endOffset="5448"/>
        <location id="R.string.rate"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="12"
            column="13"
            startOffset="592"
            endLine="12"
            endColumn="24"
            endOffset="603"/>
        <location id="R.string.save_your_artwork"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="150"
            column="13"
            startOffset="7861"
            endLine="150"
            endColumn="37"
            endOffset="7885"/>
        <location id="R.string.settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="137"
            column="13"
            startOffset="7340"
            endLine="137"
            endColumn="28"
            endOffset="7355"/>
        <location id="R.string.soon"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="115"
            column="13"
            startOffset="5991"
            endLine="115"
            endColumn="24"
            endOffset="6002"/>
        <location id="R.string.title_about"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="220"
            column="13"
            startOffset="12015"
            endLine="220"
            endColumn="31"
            endOffset="12033"/>
        <location id="R.string.title_favorites"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="222"
            column="13"
            startOffset="12113"
            endLine="222"
            endColumn="35"
            endOffset="12135"/>
        <location id="R.string.title_gallery"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="221"
            column="13"
            startOffset="12062"
            endLine="221"
            endColumn="33"
            endOffset="12082"/>
        <location id="R.string.title_more"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="223"
            column="13"
            startOffset="12168"
            endLine="223"
            endColumn="30"
            endOffset="12185"/>
        <location id="R.style.AppTheme_AppBarOverlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="19"
            column="12"
            startOffset="769"
            endLine="19"
            endColumn="41"
            endOffset="798"/>
        <location id="R.style.AppTheme_NoActionBar_AppBarOverlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="23"
            column="12"
            startOffset="947"
            endLine="23"
            endColumn="53"
            endOffset="988"/>
        <location id="R.style.AppTheme_PopupOverlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="21"
            column="12"
            startOffset="863"
            endLine="21"
            endColumn="40"
            endOffset="891"/>
        <location id="R.style.DarkBodyText"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="56"
            column="12"
            startOffset="2613"
            endLine="56"
            endColumn="31"
            endOffset="2632"/>
        <location id="R.style.DarkButtonStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="41"
            column="12"
            startOffset="1972"
            endLine="41"
            endColumn="34"
            endOffset="1994"/>
        <location id="R.style.DarkCardStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="34"
            column="12"
            startOffset="1709"
            endLine="34"
            endColumn="32"
            endOffset="1729"/>
        <location id="R.style.DarkNavigationStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="63"
            column="12"
            startOffset="2874"
            endLine="63"
            endColumn="38"
            endOffset="2900"/>
        <location id="R.style.DarkTitleText"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="49"
            column="12"
            startOffset="2337"
            endLine="49"
            endColumn="32"
            endOffset="2357"/>
        <location id="R.style.PremiumButtonStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="67"
            column="12"
            startOffset="2785"
            endLine="67"
            endColumn="37"
            endOffset="2810"/>
        <location id="R.style.PremiumCardStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="39"
            column="12"
            startOffset="1602"
            endLine="39"
            endColumn="35"
            endOffset="1625"/>
        <location id="R.style.PremiumNavItemShape"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="33"
            column="12"
            startOffset="1417"
            endLine="33"
            endColumn="38"
            endOffset="1443"/>
        <location id="R.style.PremiumNavTextAppearance"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="26"
            column="12"
            startOffset="1093"
            endLine="26"
            endColumn="43"
            endOffset="1124"/>
        <location id="R.style.PremiumSubtitleText"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="59"
            column="12"
            startOffset="2464"
            endLine="59"
            endColumn="38"
            endOffset="2490"/>
        <location id="R.style.PremiumTitleText"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="47"
            column="12"
            startOffset="1914"
            endLine="47"
            endColumn="35"
            endOffset="1937"/>
        <location id="R.style.SettingsTheme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="20"
            column="12"
            startOffset="1068"
            endLine="20"
            endColumn="32"
            endOffset="1088"/>
        <location id="R.style.SwitchTheme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="85"
            column="12"
            startOffset="3661"
            endLine="85"
            endColumn="30"
            endOffset="3679"/>
        <entry
            name="model"
            string="array[Thumbnails1(D),Thumbnails2(D),Thumbnails3(D),Thumbnails4(D),Thumbnails5(D),Thumbnails6(D)],attr[actionBarSize(R),colorPrimary(R)],color[modern_primary(U),modern_surface(U),black(U),divider(U),white(U),card_background(U),background(U),textSecondary(U),ripple_color(U),modern_accent_light(U),modern_accent(U),brown(U),deep_blue(U),deep_green(U),deep_orange(U),deep_pink(U),deep_purple(U),modern_background_light(D),modern_background(U),gray(U),nature_color(U),animals_color(U),text_secondary(U),cartoons_color(U),flowers_color(U),foods_color(U),transport_color(U),light_blue(U),light_green(U),light_orange(U),light_pink(U),light_purple(U),modern_card_bg(U),gradient_center(U),gradient_end(U),gradient_start(U),colorAccent(U),colorPrimaryDark(U),modern_primary_dark(D),text_primary(U),colorPrimary(U),red(U),border_color(D),yellow(U),background_color(U),text_on_primary(U),nav_gallery_color(U),nav_favorites_color(U),nav_more_color(U),dialog_background(U),light_gray(U),nav_home_color(D),nav_categories_color(D),light(D),primary_text(D),secondary_text(D),textPrimary(U),shadow_light(D),shadow_medium(D),shadow_dark(D),dark_background(D),dark_surface(D),dark_card_bg(D),dark_text_primary(D),dark_text_secondary(D),dark_text_on_primary(D),dark_nav_home_color(D),dark_nav_categories_color(D),dark_nav_gallery_color(D),dark_nav_favorites_color(D),dark_nav_more_color(D),dark_flowers_color(D),dark_cartoons_color(D),dark_animals_color(D),dark_foods_color(D),dark_transport_color(D),dark_nature_color(D),dark_modern_primary(D),dark_modern_accent(D),dark_modern_accent_light(D),surface_color(D),status_bar_color(U),navigation_bar_color(U),button_background(D),button_text(D),divider_color(D),error_color(D),warning_color(D),success_color(D)],dimen[activity_horizontal_margin(D),activity_vertical_margin(D),bottom_nav_height(D),ad_margin_bottom(D)],drawable[logo(U),about(D),beautiful_ripple_effect(U),beautiful_title_background(U),black(U),border(U),bottom_nav_background(D),bottom_nav_border(D),bottom_nav_item_background(D),brown(U),decorative_circle(D),deep_blue(U),deep_green(U),deep_orange(U),deep_pink(U),deep_purple(U),enhanced_bottom_nav_background(U),favorites_header_background(U),gallery_header_background(U),gp1(U),gp1_press(U),gp2(U),gp2_press(U),gp3(U),gp3_press(U),gp4(U),gp4_press(U),gp5(U),gp5_press(U),gp6(U),gp6_press(U),gradient_background(D),gray(U),ic_ads_modern(D),ic_animals_modern(U),ic_app_logo(D),ic_arrow_forward(U),ic_cartoons_modern(U),ic_categories_modern(U),ic_clear_modern(U),ic_dark_mode(D),ic_delete(D),ic_delete_modern(U),ic_display_modern(U),ic_empty_gallery(U),ic_empty_heart(U),ic_exit_app(U),ic_export_modern(U),ic_favorites_modern(U),ic_flowers_modern(U),ic_foods_modern(U),ic_gallery_modern(U),ic_home_modern(U),ic_image_quality(D),ic_info(D),ic_info_modern(U),ic_language(D),ic_launcher_background(D),ic_launcher_foreground(D),ic_more_modern(U),ic_nature_modern(U),ic_notifications(D),ic_privacy_modern(U),ic_refresh(D),ic_refresh_modern(U),ic_save(D),ic_settings_modern(U),ic_share_modern(U),ic_star_modern(U),ic_storage(D),ic_support_modern(D),ic_tip_modern(U),ic_transport_modern(U),ic_vibration(D),ic_volume_modern(U),ic_volume_up(D),light_blue(U),light_green(U),light_orange(U),light_overlay(U),light_pink(U),light_purple(U),main(D),modern_card_background(D),modern_card_overlay(D),modern_gradient_background(U),modern_ripple_effect(D),more_header_background(U),more_item_background(U),nav_active_indicator(D),nav_header_pattern(D),navitemcolor(D),premium_button_background(D),premium_card_overlay(D),premium_logo_background(D),premium_nav_header_background(D),premium_nav_item_background(D),premium_nav_item_color(D),premium_nav_pattern(D),premium_nav_ripple_effect(D),premium_ripple_effect(D),premium_text_background(D),premium_version_badge(D),pressed_no_corners(D),privacy(D),red(U),save_icon(U),select_color(U),setting_item_background(D),settings(D),settings_item_background(U),share(D),share_icon(U),small_logo(U),sound_off(U),sound_on(U),star(D),undo_icon(U),white(U),yellow(U),gp1_1(U),gp1_2(D),gp1_3(D),gp1_4(D),gp1_5(D),gp1_6(D),gp2_1(D),gp2_2(D),gp2_3(D),gp2_4(D),gp2_5(D),gp2_6(D),gp2_7(D),gp2_8(D),gp3_1(D),gp3_10(D),gp3_11(D),gp3_12(D),gp3_13(D),gp3_14(D),gp3_15(D),gp3_16(D),gp3_17(D),gp3_18(D),gp3_19(D),gp3_2(D),gp3_20(D),gp3_21(D),gp3_22(D),gp3_23(D),gp3_24(D),gp3_25(D),gp3_26(D),gp3_27(D),gp3_28(D),gp3_29(D),gp3_3(D),gp3_30(D),gp3_31(D),gp3_32(D),gp3_33(D),gp3_34(D),gp3_35(D),gp3_36(D),gp3_37(D),gp3_38(D),gp3_39(D),gp3_4(D),gp3_40(D),gp3_41(D),gp3_42(D),gp3_43(D),gp3_44(D),gp3_45(D),gp3_46(D),gp3_47(D),gp3_48(D),gp3_49(D),gp3_5(D),gp3_50(D),gp3_51(D),gp3_52(D),gp3_53(D),gp3_54(D),gp3_55(D),gp3_56(D),gp3_57(D),gp3_58(D),gp3_59(D),gp3_6(D),gp3_60(D),gp3_61(D),gp3_62(D),gp3_63(D),gp3_64(D),gp3_65(D),gp3_66(D),gp3_67(D),gp3_68(D),gp3_69(D),gp3_7(D),gp3_70(D),gp3_71(D),gp3_72(D),gp3_73(D),gp3_74(D),gp3_75(D),gp3_76(D),gp3_77(D),gp3_78(D),gp3_79(D),gp3_8(D),gp3_80(D),gp3_81(D),gp3_82(D),gp3_83(D),gp3_84(D),gp3_85(D),gp3_86(D),gp3_9(D),gp4_1(D),gp4_2(D),gp4_3(D),gp4_4(D),gp4_5(D),gp4_6(D),gp5_1(D),gp5_2(D),gp5_3(D),gp5_4(D),gp5_5(D),gp5_6(D),gp6_1(D),gp6_2(D),gp6_3(D),gp6_4(D),gp6_5(D),gp6_6(D)],font[blabeloo(U),blabeloo_regular(R)],id[toolbar(U),activity_container(D),activity_content(U),navigationView(D),header_card(D),app_title(D),app_subtitle(D),c_1(U),text_card_name1(U),c_2(U),text_card_name4(U),c_3(U),text_card_name2(U),c_4(U),text_card_name5(U),c_5(U),text_card_name3(U),c_6(U),text_card_name6(U),adView(U),gridView(U),empty_favorites_card(U),btn_browse_categories(U),favorites_recycler_view(U),favorites_actions(U),btn_clear_favorites(U),btn_export_favorites(U),empty_state_card(U),btn_start_coloring(U),gallery_recycler_view(U),gallery_actions(U),btn_clear_all(U),btn_share_all(U),deep_orange(U),light_pink(U),light_green(U),yellow(U),light_blue(U),deep_purple(U),light_orange(U),white(U),red(U),deep_pink(U),deep_green(U),light_purple(U),deep_blue(U),brown(U),gray(U),black(U),select_color(U),relative_layout(U),coringImage(U),fragment_container(D),nav_home(U),nav_categories(D),nav_gallery(U),nav_favorites(U),nav_more(U),more_settings(U),more_about(U),more_share(U),more_rate(U),more_privacy(U),switch_sound_effects(U),switch_background_music(U),switch_auto_save(U),switch_show_grid(U),switch_dark_mode(U),language_card(U),language_text(U),quality_card(U),quality_text(U),btn_privacy_policy(U),btn_reset_ads(U),switch_vibration(U),switch_fast_animations(U),switch_color_suggestions(U),logo(D),bottom_navigation(D),nav_gallery_icon(U),nav_gallery_text(U),nav_gallery_indicator(U),nav_favorites_icon(U),nav_favorites_text(U),nav_favorites_indicator(U),nav_more_icon(U),nav_more_text(U),nav_more_indicator(U),bottom_navigation_container(U),dialog_card(U),dialog_title(U),dialog_message(U),btn_cancel(U),btn_exit(U),main_content(D),nav_home_icon(D),nav_home_indicator(D),nav_home_text(D),nav_categories_icon(D),nav_categories_indicator(D),nav_categories_text(D),image(U),imageView(D),nav_logo(D),mainmenu(U),about(U),share(U),rate(U),privacyPolicy(U),ads(U),categories(U),gallery(U),favorites(U),settings(U),support(U),action_share(U),action_save(U),action_mute(U),action_undo(U)],layout[activity_about(U),simple_bottom_navigation(U),activity_base(D),premium_nav_header(D),activity_base_no_drawer(U),activity_categories(U),activity_category_items(U),activity_favorites(U),activity_gallery(U),activity_main(U),activity_main_with_bottom_nav(D),activity_more(U),activity_settings(U),activity_splash(D),bottom_navigation(D),bottom_navigation_bar(D),dialog_exit_confirmation(U),enhanced_bottom_navigation(D),grid_item_layout(U),nav_header_drawer(D)],menu[premium_nav_drawer(D),activity_nav_drawer(D),share_save_menu(U)],mipmap[ic_launcher(D),ic_launcher_foreground(D),ic_launcher_round(D)],raw[background_1(D),background_2(D),background_3(D),background_4(D),background_5(D),background_6(D),click(U)],string[app_name(U),appbar_scrolling_view_behavior(R),app_version_about(U),app_description_title(U),app_description_text(U),app_features_title(U),feature_6_categories(U),feature_child_friendly(U),feature_save_share(U),feature_modern_design(U),developer_info_title(U),developer_info_text(U),choose_category(U),flowers(U),flowers_desc(U),cartoons(U),cartoons_desc(U),animals(U),animals_desc(U),foods(U),foods_desc(U),transport(U),transport_desc(U),nature(U),nature_desc(U),banner_unit_id(U),favorites_title(U),favorites_subtitle(U),no_favorite_images(U),add_favorites_instruction(U),browse_categories(U),clear_favorites(U),export_list(U),favorites_tip(U),gallery_title(U),gallery_subtitle(U),no_artworks_yet(U),start_coloring_save_message(U),start_coloring_button(U),clear_all(U),share_all(U),more_options_title(U),more_options_subtitle(U),settings_section_title(U),settings_title(U),settings_subtitle(U),app_info_title(U),about_app_title(U),about_app_subtitle(U),share_support_title(U),share_app_title(U),share_app_subtitle(U),rate_app_title(U),rate_app_subtitle(U),privacy_policy_title(U),privacy_policy_subtitle(U),sound_settings(U),sound_effects(U),background_music(U),display_settings(U),auto_save(U),show_grid(U),dark_mode(U),language_quality(U),app_language(U),language_english(U),image_quality(U),quality_high(U),privacy_security(U),privacy_policy(U),reset_ads(U),app_info(U),app_version(U),app_version_text(U),build_number(U),build_date(U),advanced_features(U),vibration_on_coloring(U),fast_animations(U),automatic_color_suggestions(U),nav_gallery(U),nav_favorites(U),nav_more(U),exit_confirmation(U),exit_message(U),cancel(U),exit_button(U),app_subtitle(D),nav_home(U),main(D),about(D),Share_The_App(U),rate(D),privacypolicy(D),ads(D),action_share(U),action_save(U),action_mute(U),action_undo(U),nav_drawer_opened(D),nav_drawer_closed(D),save(U),permission(U),ok(U),about_text(D),notification_from(U),notification_body(U),notification_permission_granted(U),notification_permission_denied(U),nav_categories(D),gallery(D),about_title(U),sound_effects_enabled(U),sound_effects_disabled(U),background_music_enabled(U),background_music_disabled(U),auto_save_enabled(U),auto_save_disabled(U),helper_grid_enabled(U),helper_grid_disabled(U),dark_mode_enabled(U),dark_mode_disabled(U),vibration_enabled(U),vibration_disabled(U),fast_animations_enabled(U),fast_animations_disabled(U),color_suggestions_enabled(U),color_suggestions_disabled(U),quality_medium(U),quality_low(U),image_quality_title(U),image_quality_changed(U),quality_settings_applied(U),quality_settings_error(U),dark_mode_error(U),language_arabic(U),pub_id(D),appId(D),interstitial_unit_id(U),app_open_ad_unit_id(U),soon(D),gdpr_privacypolicy(D),no_data(D),dialog_close(D),google_partners(D),eu_consent_text(D),eu_consent_question(D),eu_consent_yes(D),eu_consent_no(D),action_remove_ads(D),eu_consent_change_setting(D),learn_more(D),soundon(U),soundoff(U),nomore(U),chooseColor(U),settings(D),language_settings(D),my_artworks(D),no_saved_images(D),save_your_artwork(D),export_gallery(D),favorites(D),no_favorites(D),add_favorites_message(D),export_favorites(D),more(D),app_features(D),support_feedback(U),share_text_english(U),share_text_arabic(U),share_chooser_title(U),language_changed(D),choose_language(U),choose_quality(D),error_loading(D),error_saving(D),error_language_change(D),title_settings(U),title_about(D),title_gallery(D),title_favorites(D),title_more(D),title_categories(U),welcome_message(U)],style[AppTheme(U),AppTheme_NoActionBar(U),ThemeOverlay_AppCompat_Dark_ActionBar(R),ThemeOverlay_AppCompat_Light(R),ThemeOverlay_AppCompat_Dark(R),PremiumNavItemShape(D),PremiumNavTextAppearance(D),ModernSwitchStyle(U),Theme_AppCompat_Light_DarkActionBar(R),AppTheme_AppBarOverlay(D),AppTheme_PopupOverlay(D),AppTheme_NoActionBar_AppBarOverlay(D),TextAppearance_AppCompat_Medium(E),PremiumCardStyle(D),PremiumTitleText(D),PremiumSubtitleText(D),PremiumButtonStyle(D),Widget_AppCompat_Button(E),SwitchTheme(D),Widget_AppCompat_CompoundButton_Switch(R),Theme_AppCompat_DayNight_DarkActionBar(R),SettingsTheme(D),DarkCardStyle(D),CardView(E),DarkButtonStyle(D),DarkTitleText(D),DarkBodyText(D),DarkNavigationStyle(D)],xml[gma_ad_services_config(U)];0^dd^de^df^e0^e1^e2,1^e3^e4^e5^e6^e7^e8^e9^ea,2^eb^f6^101^10c^117^122^12d^138^140^ec^ed^ee^ef^f0^f1^f2^f3^f4^f5^f7^f8^f9^fa^fb^fc^fd^fe^ff^100^102^103^104^105^106^107^108^109^10a^10b^10d^10e^10f^110^111^112^113^114^115^116^118^119^11a^11b^11c^11d^11e^11f^120^121^123^124^125^126^127^128^129^12a^12b^12c^12e^12f^130^131^132^133^134^135^136^137^139^13a^13b^13c^13d^13e^13f,3^141^142^143^144^145^146,4^147^148^149^14a^14b^14c,5^14d^14e^14f^150^151^152,d^28,34^1a,58^9,59^2d,5a^1a,5b^8,5c^35,5d^b,5f^12,67^8,68^9,69^a^b,6a^a^c,6b^d^e^f^34^1e,6d^10^8^11^12,6e^13^a^b,70^14^a^b,71^15^a^b,72^16^a^b,73^17^a^b,74^18^a^b,79^78^2c,7b^7a^2c,7d^7c^2c,7f^7e^2c,81^80^2c,83^82^2c,84^19^1a,85^1b^a^b,86^1c,87^1d,88^8,89^1e,8a^1f,8b^20,8c^1e,8f^1e,90^1f,91^1e,92^1e,95^1d,96^20,97^21,98^1f,99^8,9c^12,a1^1c,a3^1c,a5^1e,a7^1e,a8^1f,a9^12,ab^22,ac^12,ad^22,af^8,b1^23^a^b,b2^24^a^b,b3^25^a^b,b5^26^a^b,b6^27^a^b,b8^28,ba^29^2a^2b,bb^8,bd^10,c0^2c^2d,c1^8^2e^12,c3^2e^8,c4^29^2a^2b,c5^8^10,c6^8^2f,c9^8,cc^30^2c,ce^31^a^b,d0^26^25^23,d1^d^32,db^c^a^b,dc^33^a^b,153^154,1cc^1a^2a8^ba^6^2a9^1ee^d6^153^1ed^1ef^1f0^8^1f1^2f^1f2^96^20^1f3^8a^1f^1f4^87^1d^1f5^a9^12^1f6^1f7^1f8^1cd,1cd^75^99^153^245^98^23d^95^23e^a0^23f,1ce^7^2aa^9^1cf^c5^c6^2ab^8^2ac^1e0,1cf^c4^c7^c3^d6^153^1ed^cb^96^8a^87,1d0^7^6^2aa^2a9,1d1^1a^ba^d6^153^1ed^35^1f9^1ee^67^79^b4^20^96^68^1fa^2f^1fb^1e^7b^1f^8a^1fc^1fd^7d^1d^87^1fe^1ff^7f^21^97^200^201^81^22^ad^202^203^83^1c^a1^204^205^206^1cd^b^2d,1d2^b^206,1d3^1a^2a8^ba^6^2a9^1ee^76^95^153^207^208^92^1e^209^2f^20a^67^12^8b^20b^9^8c^20c^94^20d^ac^20e^1cd,1d4^1a^2a8^ba^6^2a9^1ee^77^98^153^20f^210^91^1e^211^2f^212^67^8^96^213^9^8f^214^a8^215^1cd,1d5^72^b5^b2^dc^b1^74^b3^db^ce^73^71^b6^70^6e^85^69^d0^206,1d6^1a^6b^6d^8^99^153^1f^8b^2f^1c^98^12^95^1e^a0,1d7^1a^bc^a0^153^216^217^218^8^bd^a7^219^2f^21a^1e^89^21b^1f^9c^21c^21d^21e^1c^a8^21f^220^12^a9^221^222^a3^223^224^1cd,1d8^1a^2a8^ba^6^2a9^1ee^af^8^153^225^226^2f^2ad^227^90^1f^228^229^22a^22b^a7^1d^22c^22d^22e^89^1e^22f^230^a3^1c^231^d3^232^233^a5^9c^12^234^235^236^237^238^a9^21^239^23a^23b^23c^1cd,1d9^30^9f,1da^d^1e^98^153^23d^2f^36^95^23e^37^a0^23f^38,1db^6c^36^98^153^23d^2f^37^95^23e^38^a0^23f,1dc^39^11^93^12^153^240^2f^241^1e^67^3a^242^243,1dd^1a^6b^c8^3b^99^be^1b4^153^2f^3c^8b^1b7^36^98^1a4^37^95^1a7^38^a0^1aa,1de^6a^dd,1df^ba^2aa^bf^d6^153^1ed^244^96^8a,1e0^99^246^8b^98^95^9c^247^a8^248^a9^249^a7^a3^24a^ab^86^24b,1e1^99^246^9c^247^a8^248^a9^249^a3^24a^a7^24b,1e2^d5^24c^cf^24d^d8^24e^da^24f,1e3^9e^1e4,1e5^9e^1e4,2a6^2ae^30^2d^2c^e^40^f^2ba^8^59^12^34^2f^1e^d^5a,2a7^2a6,2ac^2b2^153,2ad^8^1e^2b9^12^2f,2af^2a8,2b0^2a9,2b1^2a8,2b3^28,2b4^153,2b5^153,2b6^2b7^c1^153,2b8^2b9^8^1e,2bb^2a6^34^8^12,2bc^2bd^d,2be^2b7^5b^5c^153,2bf^2f^153,2c0^1e^153,2c1^d;;;"/>
    </map>

</incidents>
