{"logs": [{"outputFile": "com.alwan.kids2025.app-mergeReleaseResources-56:/values-large-v4/values-large-v4.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\36dc858f18a2a1e2b92bb4036b7b9aba\\transformed\\material-1.12.0\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,285,407", "endColumns": "113,115,121,133", "endOffsets": "164,280,402,536"}, "to": {"startLines": "11,12,13,14", "startColumns": "4,4,4,4", "startOffsets": "752,866,982,1104", "endColumns": "113,115,121,133", "endOffsets": "861,977,1099,1233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40acce1c3a2b617941018f107a306df6\\transformed\\appcompat-1.7.0\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,185,256,326,396,464,532,636", "endColumns": "58,70,70,69,69,67,67,103,115", "endOffsets": "109,180,251,321,391,459,527,631,747"}}]}]}