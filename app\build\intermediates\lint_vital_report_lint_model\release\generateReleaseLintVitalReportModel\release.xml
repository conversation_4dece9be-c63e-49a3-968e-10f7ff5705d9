<variant
    name="release"
    package="com.alwan.kids2025"
    minSdkVersion="23"
    targetSdkVersion="34"
    mergedManifest="build\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-8.9.1;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\release\compileReleaseJavaWithJavac\classes;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.alwan.kids2025"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out"
      generatedResourceFolders="build\generated\res\processReleaseGoogleServices;build\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ee07fe93b2c9db13af851bd253b5328\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
