<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center_horizontal|center_vertical"
    android:background="@color/divider"
    android:gravity="center_horizontal|center_vertical"
    android:orientation="vertical">

    <GridView
        android:id="@+id/gridView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="center_horizontal|center_vertical"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="10dp"

        android:layout_weight="1"
        android:clickable="true"
        android:columnWidth="150dp"
        android:drawSelectorOnTop="true"
        android:focusable="true"
        android:gravity="center_horizontal|center_vertical"
        android:horizontalSpacing="2dp"
        android:numColumns="auto_fit"
        android:stretchMode="columnWidth"
        android:verticalSpacing="2dp" />

    <!-- Bottom Ad Banner - Fixed positioning -->
    <com.google.android.gms.ads.AdView
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:background="@android:color/white"
        android:elevation="4dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id"/>
</LinearLayout>