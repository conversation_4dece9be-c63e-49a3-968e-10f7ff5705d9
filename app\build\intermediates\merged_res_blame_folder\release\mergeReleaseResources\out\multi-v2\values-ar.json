{"logs": [{"outputFile": "com.alwan.kids2025.app-mergeReleaseResources-56:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\952446e2a2fa218bc85ba4e6563abcfd\\transformed\\jetified-play-services-ads-22.0.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,342,410,510,563,677,734,846,931,969,1048,1080,1111,1154,1222,1262", "endColumns": "40,47,53,67,99,52,113,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,409,509,562,676,733,845,930,968,1047,1079,1110,1153,1221,1261,1317"}, "to": {"startLines": "258,259,260,281,282,283,284,285,286,287,306,307,308,309,310,311,312,354", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19523,19568,19620,20862,20934,21038,21095,21213,21274,21390,22632,22674,22757,22793,22828,22875,22947,25818", "endColumns": "44,51,57,71,103,56,117,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "19563,19615,19673,20929,21033,21090,21208,21269,21385,21474,22669,22752,22788,22823,22870,22942,22986,25873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f1a8cebdf235588805d6f74854d945d7\\transformed\\preference-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,265,351,484,653,735", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "167,260,346,479,648,730,810"}, "to": {"startLines": "133,155,276,291,339,349,350", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "10204,11842,20496,21645,24816,25456,25538", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "10266,11930,20577,21773,24980,25533,25613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4630b1237475cc69c83d8707fc0d9479\\transformed\\browser-1.4.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "134,160,161,162", "startColumns": "4,4,4,4", "startOffsets": "10271,12195,12293,12401", "endColumns": "99,97,107,101", "endOffsets": "10366,12288,12396,12498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cbd131f1b1e0c9680dc8c1d103e011d9\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "9062", "endColumns": "129", "endOffsets": "9187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\36dc858f18a2a1e2b92bb4036b7b9aba\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "2,86,87,88,89,90,104,105,112,142,143,159,185,189,196,197,198,199,200,201,202,203,204,205,206,207,208,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,289,316,317,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,5900,5978,6054,6138,6230,7325,7426,7870,10836,10895,12104,13941,14195,14625,14725,14788,14853,14914,14982,15044,15102,15216,15276,15337,15394,15467,15770,15851,15943,16050,16148,16228,16376,16457,16538,16666,16755,16831,16884,16938,17004,17082,17162,17233,17315,17387,17461,17534,17604,17713,17804,17875,17965,18060,18134,18217,18310,18359,18440,18509,18595,18680,18742,18806,18869,18938,19047,19157,19254,19354,19411,21516,23195,23274,24111", "endLines": "9,86,87,88,89,90,104,105,112,142,143,159,185,189,196,197,198,199,200,201,202,203,204,205,206,207,208,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,289,316,317,329", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,5973,6049,6133,6225,6308,7421,7540,7942,10890,10953,12190,14005,14257,14720,14783,14848,14909,14977,15039,15097,15211,15271,15332,15389,15462,15585,15846,15938,16045,16143,16223,16371,16452,16533,16661,16750,16826,16879,16933,16999,17077,17157,17228,17310,17382,17456,17529,17599,17708,17799,17870,17960,18055,18129,18212,18305,18354,18435,18504,18590,18675,18737,18801,18864,18933,19042,19152,19249,19349,19406,19464,21591,23269,23344,24182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d1273d61679a02c43f1de22c3f405d6f\\transformed\\core-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "94,95,96,97,98,99,100,338", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6473,6566,6668,6763,6866,6969,7071,24715", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "6561,6663,6758,6861,6964,7066,7180,24811"}}, {"source": "Z:\\alwan6\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "6,10,170,169,14,91,47,44,3,2,50,236,154,35,184,140,128,194,193,159,195,71,168,1,243,72,192,188,63,99,98,187,62,97,96,237,189,73,13,139,127,52,134,207,208,229,238,109,108,65,103,102,119,201,200,38,213,211,212,45,43,41,40,42,78,76,77,155,149,239,186,107,106,152,234,240,233,196,197,199,198,138,126,141,129,88,225,224,36,39,101,100,67,116,115,123,204,122,66,46,9,158,164,163,146,143,131,82,5,4,84,83,81,85,226,37,235,153,147,51,55,54,57,56,12,8,69,177,176,68,33,112,114,113,117,118,11,175,174,70,7,148,60,165,167,166,230,173,172,181,171,180,64,34,61,95,94,49,48,228,227,160,217,221,219,218,220,216,142,130,105,104,185,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "285,482,8327,8270,641,4283,2243,1986,117,68,2405,12021,7516,1323,9180,6926,6420,9743,9680,7728,9983,3507,8210,17,12423,3561,9611,9465,3042,4757,4686,9381,2979,4605,4525,12117,9523,3616,599,6880,6365,2508,6664,10630,10686,11695,12176,5502,5421,3148,5047,4978,6060,10405,10340,1493,10886,10773,10831,2051,1914,1722,1596,1831,3869,3697,3757,7599,7330,12233,9312,5334,5248,7413,11886,12288,11827,10046,10118,10272,10197,6836,6312,6973,6476,4217,11394,11331,1370,1541,4903,4829,3261,5824,5763,6221,10533,6164,3200,2155,439,7687,7951,7884,7135,7067,6583,3989,224,162,4097,4040,3942,4148,11469,1441,11955,7460,7190,2452,2621,2562,2765,2683,561,389,3379,8793,8728,3316,1218,5616,5715,5664,5896,5980,521,8655,8597,3438,341,7254,2871,8026,8142,8088,11743,8524,8464,9073,8399,8897,3096,1271,2919,4447,4370,2349,2294,11631,11537,7784,11044,11247,11147,11097,11200,10990,7016,6527,5182,5117,9240,6726", "endLines": "6,10,170,169,28,91,47,44,3,2,50,236,154,35,184,140,128,194,193,159,195,71,168,1,243,72,192,188,63,99,98,187,62,97,96,237,189,73,13,139,127,52,134,207,208,229,238,109,108,65,103,102,119,201,200,38,213,211,212,45,43,41,40,42,78,76,77,155,149,239,186,107,106,152,234,240,233,196,197,199,198,138,126,141,129,88,225,224,36,39,101,100,67,116,115,123,204,122,66,46,9,158,164,163,146,143,131,82,5,4,84,83,81,85,226,37,235,153,147,51,55,54,57,56,12,8,69,177,176,68,33,112,114,113,117,118,11,175,174,70,7,148,60,165,167,166,230,173,172,181,171,180,64,34,61,95,94,49,48,228,227,160,217,221,219,218,220,216,142,130,105,104,185,135", "endColumns": "54,37,70,55,59,51,49,63,43,47,45,94,81,45,58,45,54,238,61,54,61,52,58,49,62,53,67,56,52,70,69,82,61,79,78,57,49,51,40,44,53,50,60,54,54,46,55,80,79,50,68,67,69,95,63,46,68,56,53,102,70,107,124,81,44,58,110,58,55,53,67,85,84,45,67,99,57,70,77,66,73,42,51,41,49,42,73,61,69,53,73,72,53,70,59,51,65,55,59,86,41,39,73,65,53,42,51,49,59,60,49,55,45,43,66,50,64,54,62,54,60,57,79,80,36,48,57,75,63,61,51,46,46,49,82,78,38,71,56,67,46,74,46,60,66,52,49,71,58,62,63,174,50,50,58,76,75,54,53,62,92,61,51,51,51,48,45,52,49,54,64,63,70,77", "endOffsets": "335,515,8393,8321,1154,4330,2288,2045,156,111,2446,12111,7593,1364,9234,6967,6470,9977,9737,7778,10040,3555,8264,62,12481,3610,9674,9517,3090,4823,4751,9459,3036,4680,4599,12170,9568,3663,635,6920,6414,2554,6720,10680,10736,11737,12227,5578,5496,3194,5111,5041,6125,10496,10399,1535,10950,10825,10880,2149,1980,1825,1716,1908,3909,3751,3863,7653,7381,12282,9375,5415,5328,7454,11949,12383,11880,10112,10191,10334,10266,6874,6359,7010,6521,4255,11463,11388,1435,1590,4972,4897,3310,5890,5818,6268,10594,6215,3255,2237,476,7722,8020,7945,7184,7105,6630,4034,279,218,4142,4091,3983,4187,11531,1487,12015,7510,7248,2502,2677,2615,2840,2759,593,433,3432,8864,8787,3373,1265,5658,5757,5709,5974,6054,555,8722,8649,3501,383,7324,2913,8082,8204,8136,11788,8591,8518,9131,8458,9067,3142,1317,2973,4519,4441,2399,2343,11689,11625,7841,11091,11294,11194,11141,11241,11038,7061,6577,5242,5176,9306,6799"}, "to": {"startLines": "10,38,39,40,41,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,91,92,93,101,102,103,106,107,108,109,110,111,113,114,135,136,137,138,139,140,141,144,145,146,147,148,149,150,151,152,153,154,156,157,158,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,186,187,188,190,191,192,193,194,195,209,210,211,257,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,277,278,279,280,288,290,292,293,294,295,296,297,298,299,300,301,302,303,304,305,313,314,318,319,320,321,322,323,324,325,326,327,328,330,331,332,333,334,335,336,337,340,341,342,343,344,345,346,347,348,351,352,353,355", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3224,3262,3333,3389,3893,3945,3995,4059,4103,4151,4197,4292,4374,4420,4479,4525,4580,4819,4881,4936,4998,5051,5110,5160,5223,5277,5345,5402,5455,5526,5596,5679,5741,5821,6313,6371,6421,7185,7226,7271,7545,7596,7657,7712,7767,7814,7947,8028,10371,10422,10491,10559,10629,10725,10789,10958,11027,11084,11138,11241,11312,11420,11545,11627,11672,11731,11935,11994,12050,12503,12571,12657,12742,12788,12856,12956,13014,13085,13163,13230,13304,13347,13399,13441,13491,13534,13608,13670,13740,13794,13868,14010,14064,14135,14262,14314,14380,14436,14496,14583,15590,15630,15704,19469,19678,19721,19773,19823,19883,19944,19994,20050,20096,20140,20207,20258,20323,20378,20441,20582,20643,20701,20781,21479,21596,21778,21836,21912,21976,22038,22090,22137,22184,22234,22317,22396,22435,22507,22564,22991,23038,23349,23396,23457,23524,23577,23627,23699,23758,23821,23885,24060,24187,24238,24297,24374,24450,24505,24559,24622,24985,25047,25099,25151,25203,25252,25298,25351,25401,25618,25683,25747,25878", "endLines": "10,38,39,40,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,91,92,93,101,102,103,106,107,108,109,110,111,113,114,135,136,137,138,139,140,141,144,145,146,147,148,149,150,151,152,153,154,156,157,158,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,186,187,188,190,191,192,193,194,195,209,210,211,257,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,277,278,279,280,288,290,292,293,294,295,296,297,298,299,300,301,302,303,304,305,313,314,318,319,320,321,322,323,324,325,326,327,328,330,331,332,333,334,335,336,337,340,341,342,343,344,345,346,347,348,351,352,353,355", "endColumns": "54,37,70,55,59,51,49,63,43,47,45,94,81,45,58,45,54,238,61,54,61,52,58,49,62,53,67,56,52,70,69,82,61,79,78,57,49,51,40,44,53,50,60,54,54,46,55,80,79,50,68,67,69,95,63,46,68,56,53,102,70,107,124,81,44,58,110,58,55,53,67,85,84,45,67,99,57,70,77,66,73,42,51,41,49,42,73,61,69,53,73,72,53,70,59,51,65,55,59,86,41,39,73,65,53,42,51,49,59,60,49,55,45,43,66,50,64,54,62,54,60,57,79,80,36,48,57,75,63,61,51,46,46,49,82,78,38,71,56,67,46,74,46,60,66,52,49,71,58,62,63,174,50,50,58,76,75,54,53,62,92,61,51,51,51,48,45,52,49,54,64,63,70,77", "endOffsets": "565,3257,3328,3384,3888,3940,3990,4054,4098,4146,4192,4287,4369,4415,4474,4520,4575,4814,4876,4931,4993,5046,5105,5155,5218,5272,5340,5397,5450,5521,5591,5674,5736,5816,5895,6366,6416,6468,7221,7266,7320,7591,7652,7707,7762,7809,7865,8023,8103,10417,10486,10554,10624,10720,10784,10831,11022,11079,11133,11236,11307,11415,11540,11622,11667,11726,11837,11989,12045,12099,12566,12652,12737,12783,12851,12951,13009,13080,13158,13225,13299,13342,13394,13436,13486,13529,13603,13665,13735,13789,13863,13936,14059,14130,14190,14309,14375,14431,14491,14578,14620,15625,15699,15765,19518,19716,19768,19818,19878,19939,19989,20045,20091,20135,20202,20253,20318,20373,20436,20491,20638,20696,20776,20857,21511,21640,21831,21907,21971,22033,22085,22132,22179,22229,22312,22391,22430,22502,22559,22627,23033,23108,23391,23452,23519,23572,23622,23694,23753,23816,23880,24055,24106,24233,24292,24369,24445,24500,24554,24617,24710,25042,25094,25146,25198,25247,25293,25346,25396,25451,25678,25742,25813,25951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40acce1c3a2b617941018f107a306df6\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,315", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "570,678,782,889,971,1072,1186,1266,1345,1436,1529,1621,1715,1815,1908,2003,2096,2187,2281,2360,2465,2563,2661,2769,2869,2972,3127,23113", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "673,777,884,966,1067,1181,1261,1340,1431,1524,1616,1710,1810,1903,1998,2091,2182,2276,2355,2460,2558,2656,2764,2864,2967,3122,3219,23190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a28de868e92df6a446272321a9f2ec8d\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "115,116,117,118,119,120,121,122,124,125,126,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8108,8212,8356,8478,8583,8721,8849,8960,9192,9329,9433,9583,9705,9844,9990,10054,10120", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "8207,8351,8473,8578,8716,8844,8955,9057,9324,9428,9578,9700,9839,9985,10049,10115,10199"}}]}]}