<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.test.espresso:espresso-core:3.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a650a3d52425cdb678d2f5a0308f466f\transformed\espresso-core-3.6.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-core:3.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a650a3d52425cdb678d2f5a0308f466f\transformed\espresso-core-3.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc858f18a2a1e2b92bb4036b7b9aba\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc858f18a2a1e2b92bb4036b7b9aba\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.ViksaaSkool:AwesomeSplash:v1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9207e24da08392b123ac6abd6c0bbb7\transformed\jetified-AwesomeSplash-v1.0.0\jars\classes.jar"
      resolved="com.github.ViksaaSkool:AwesomeSplash:v1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9207e24da08392b123ac6abd6c0bbb7\transformed\jetified-AwesomeSplash-v1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1a8cebdf235588805d6f74854d945d7\transformed\preference-1.2.1\jars\classes.jar"
      resolved="androidx.preference:preference:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1a8cebdf235588805d6f74854d945d7\transformed\preference-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.onesignal:OneSignal:4.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\jars\classes.jar"
      resolved="com.onesignal:OneSignal:4.8.6"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bccf3ee0ba7e6f3b41b220a780a8d541\transformed\jetified-appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bccf3ee0ba7e6f3b41b220a780a8d541\transformed\jetified-appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e31ffb1ad5782e77f0808c09672d8fb\transformed\constraintlayout-2.0.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e31ffb1ad5782e77f0808c09672d8fb\transformed\constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.daimajia.androidanimations:library:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd6f440e3969fd1ea64c29acf33e3ec1\transformed\jetified-library-1.1.3\jars\classes.jar"
      resolved="com.daimajia.androidanimations:library:1.1.3"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd6f440e3969fd1ea64c29acf33e3ec1\transformed\jetified-library-1.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.daimajia.easing:library:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e52e2b0eeaab8a6c051461aab7823916\transformed\jetified-library-1.0.1\jars\classes.jar"
      resolved="com.daimajia.easing:library:1.0.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e52e2b0eeaab8a6c051461aab7823916\transformed\jetified-library-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40acce1c3a2b617941018f107a306df6\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40acce1c3a2b617941018f107a306df6\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.jorgecastilloprz:fillableloaders:1.02@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b068faf48cdaa9c2b51996856b6422\transformed\jetified-fillableloaders-1.02\jars\classes.jar"
      resolved="com.github.jorgecastilloprz:fillableloaders:1.02"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b068faf48cdaa9c2b51996856b6422\transformed\jetified-fillableloaders-1.02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\259a268cebdd3f407f50865b627e8df7\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\259a268cebdd3f407f50865b627e8df7\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f40f044205cc14802a2ef279bbc4b28\transformed\jetified-viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f40f044205cc14802a2ef279bbc4b28\transformed\jetified-viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads:22.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\952446e2a2fa218bc85ba4e6563abcfd\transformed\jetified-play-services-ads-22.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads:22.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\952446e2a2fa218bc85ba4e6563abcfd\transformed\jetified-play-services-ads-22.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-lite:22.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-lite:22.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.ump:user-messaging-platform:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baa0cca0fed54d0ba18400ee4703e134\transformed\jetified-user-messaging-platform-3.2.0\jars\classes.jar"
      resolved="com.google.android.ump:user-messaging-platform:3.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baa0cca0fed54d0ba18400ee4703e134\transformed\jetified-user-messaging-platform-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:review:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e1c397eb241a4443f79c6d40597bc81\transformed\jetified-review-2.0.1\jars\classes.jar"
      resolved="com.google.android.play:review:2.0.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e1c397eb241a4443f79c6d40597bc81\transformed\jetified-review-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:app-update:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d58aca08b9f7aa861741aa08f020462\transformed\jetified-app-update-2.1.0\jars\classes.jar"
      resolved="com.google.android.play:app-update:2.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d58aca08b9f7aa861741aa08f020462\transformed\jetified-app-update-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:23.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:23.4.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-base:22.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\398dacc30cdc8b839071dbb8f01d6430\transformed\jetified-play-services-ads-base-22.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-base:22.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\398dacc30cdc8b839071dbb8f01d6430\transformed\jetified-play-services-ads-base-22.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-appset:16.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3eb2bb9e1e563c935afdc0a7c16c7294\transformed\jetified-play-services-appset-16.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-appset:16.0.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3eb2bb9e1e563c935afdc0a7c16c7294\transformed\jetified-play-services-appset-16.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-analytics:21.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17339a2695474267970087912e7d125e\transformed\jetified-firebase-analytics-21.6.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-analytics:21.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17339a2695474267970087912e7d125e\transformed\jetified-firebase-analytics-21.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement:21.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement:21.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-api:21.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-api:21.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk:21.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df57532705e711fdf2f44533f7c13c3d\transformed\jetified-play-services-measurement-sdk-21.6.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk:21.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df57532705e711fdf2f44533f7c13c3d\transformed\jetified-play-services-measurement-sdk-21.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f7064170f9262454fbef57b7ee7d761\transformed\jetified-firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f7064170f9262454fbef57b7ee7d761\transformed\jetified-firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.0.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-impl:21.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-impl:21.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aad2ea763ed3fa234df937284c241f78\transformed\jetified-play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aad2ea763ed3fa234df937284c241f78\transformed\jetified-play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3179903ecb9e77da44a3c8be9966642\transformed\jetified-firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3179903ecb9e77da44a3c8be9966642\transformed\jetified-firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:17.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:20.4.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:20.4.3"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:20.4.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:20.4.3"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f823b21e08d5fbfc3612da71aa60aff6\transformed\jetified-play-services-ads-identifier-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f823b21e08d5fbfc3612da71aa60aff6\transformed\jetified-play-services-ads-identifier-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:21.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:21.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:21.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab4143cbcd5ca7b295c1f1413bd4758\transformed\jetified-play-services-measurement-base-21.6.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:21.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab4143cbcd5ca7b295c1f1413bd4758\transformed\jetified-play-services-measurement-base-21.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fefb8cb7a8ae548c5c52abbd4f1ddeda\transformed\jetified-firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fefb8cb7a8ae548c5c52abbd4f1ddeda\transformed\jetified-firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:core:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fac58a8a663cbf78970492c3b8a520dd\transformed\jetified-core-1.6.1\jars\classes.jar"
      resolved="androidx.test:core:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fac58a8a663cbf78970492c3b8a520dd\transformed\jetified-core-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.1.0\b4c245baf36d1a9e7defaf3be84f7a2ad4e1c797\concurrent-futures-ktx-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.1.0"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1da3bae361dc290f47a2d0a48aea293a\transformed\jetified-ads-adservices-java-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1da3bae361dc290f47a2d0a48aea293a\transformed\jetified-ads-adservices-java-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdcbed77f954ac3f9c1cb79f7c6baef6\transformed\jetified-fragment-ktx-1.3.6\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.3.6"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdcbed77f954ac3f9c1cb79f7c6baef6\transformed\jetified-fragment-ktx-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\191d99146b9fa9a76ea31acf904a3d1e\transformed\jetified-activity-ktx-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\191d99146b9fa9a76ea31acf904a3d1e\transformed\jetified-activity-ktx-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ba8a927c733cf611c6c264cd4bfdf44\transformed\jetified-activity-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ba8a927c733cf611c6c264cd4bfdf44\transformed\jetified-activity-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af73a1faa3b19b22c8c5a442dcfedb10\transformed\media-1.0.0\jars\classes.jar"
      resolved="androidx.media:media:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af73a1faa3b19b22c8c5a442dcfedb10\transformed\media-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80c889f815ba6b4bb68da109ca61431d\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80c889f815ba6b4bb68da109ca61431d\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6636343d70f61437cc0a971343e71743\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6636343d70f61437cc0a971343e71743\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91e320a41af982c6ca1eceeffdc69f16\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91e320a41af982c6ca1eceeffdc69f16\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7e85deab252cb2f29e2144edc7b042\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7e85deab252cb2f29e2144edc7b042\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0d553d8430940559446a74bfc8608d5\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0d553d8430940559446a74bfc8608d5\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d69217ac71851dbb289072f4ad33195\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d69217ac71851dbb289072f4ad33195\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afbefbed88d109eba46227f73f370122\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afbefbed88d109eba46227f73f370122\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b694c762c16c7e119f1b0f856d10fae\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b694c762c16c7e119f1b0f856d10fae\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4630b1237475cc69c83d8707fc0d9479\transformed\browser-1.4.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4630b1237475cc69c83d8707fc0d9479\transformed\browser-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1e8a0c2dddea7f338f6603dcbe79aeb\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1e8a0c2dddea7f338f6603dcbe79aeb\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8307035faf9d76bc341d9196dce7a796\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8307035faf9d76bc341d9196dce7a796\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b83e8d2c7022a6ed5feb556d26e63d21\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b83e8d2c7022a6ed5feb556d26e63d21\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067354aeb3298878393456f8f86531a4\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067354aeb3298878393456f8f86531a4\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70f2d5d5da83795ea4989885cc6fecf5\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70f2d5d5da83795ea4989885cc6fecf5\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\769d2e6a1b1e231cbb00b788b27d0424\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\769d2e6a1b1e231cbb00b788b27d0424\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e216803f2be921019f0d06d2f9b477\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e216803f2be921019f0d06d2f9b477\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\jars\classes.jar"
      resolved="androidx.core:core:1.13.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b43baf865b6b373e6940d10c95886d67\transformed\lifecycle-livedata-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b43baf865b6b373e6940d10c95886d67\transformed\lifecycle-livedata-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bab28f2c598b88cedd7c47559987bf2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bab28f2c598b88cedd7c47559987bf2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf0e08b05adb4a06cdf6123b54d11a59\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf0e08b05adb4a06cdf6123b54d11a59\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bbaf884e97c39b245ed01184cd66764\transformed\lifecycle-viewmodel-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bbaf884e97c39b245ed01184cd66764\transformed\lifecycle-viewmodel-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\200ddee23b0f28269478fde3a256a091\transformed\jetified-lifecycle-runtime-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\200ddee23b0f28269478fde3a256a091\transformed\jetified-lifecycle-runtime-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0e47bde83e1503581a76698fd7aff78\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0e47bde83e1503581a76698fd7aff78\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941e16d4e1af434371cdefe18ef4997b\transformed\lifecycle-livedata-core-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941e16d4e1af434371cdefe18ef4997b\transformed\lifecycle-livedata-core-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\110a371c9ae6861376c800155bf81a62\transformed\lifecycle-runtime-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\110a371c9ae6861376c800155bf81a62\transformed\lifecycle-runtime-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.2\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.1\c2d86b569f10b7fc7e28d3f50c0eed97897d77a7\kotlinx-coroutines-android-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.1\63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4\kotlinx-coroutines-core-jvm-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.7.1\6333bad6f256e2ca7bc2908f586be7161a41618c\kotlinx-coroutines-play-services-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1"
      provided="true"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af12b41581c68fb35296eaab9fe67529\transformed\jetified-play-services-tasks-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af12b41581c68fb35296eaab9fe67529\transformed\jetified-play-services-tasks-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a944053718e8d3190d378d6409efec1\transformed\fragment-1.5.4\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a944053718e8d3190d378d6409efec1\transformed\fragment-1.5.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48b90eb87364798a39a00614d5f09567\transformed\jetified-core-ktx-1.10.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.10.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48b90eb87364798a39a00614d5f09567\transformed\jetified-core-ktx-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:runner:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9858a880bed600686552c34ac7a6b58\transformed\runner-1.6.1\jars\classes.jar"
      resolved="androidx.test:runner:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9858a880bed600686552c34ac7a6b58\transformed\runner-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.services:storage:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc9ce2f275f2c6ea1f878f01d292dcb\transformed\jetified-storage-1.5.0\jars\classes.jar"
      resolved="androidx.test.services:storage:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc9ce2f275f2c6ea1f878f01d292dcb\transformed\jetified-storage-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:monitor:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\797f659602688708d4c671bb487c4b87\transformed\monitor-1.7.1\jars\classes.jar"
      resolved="androidx.test:monitor:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\797f659602688708d4c671bb487c4b87\transformed\monitor-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c5cdb9f0d5d177fbca5979f659d31d2\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c5cdb9f0d5d177fbca5979f659d31d2\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5ecf0bc8f49f17e93b305d08ef88b78\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5ecf0bc8f49f17e93b305d08ef88b78\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\291969ba7792f27b49f026d4914b7fbe\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\291969ba7792f27b49f026d4914b7fbe\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0f6ded6073ce5dc403d9b4acced436\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0f6ded6073ce5dc403d9b4acced436\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.1.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.1.7"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"
      provided="true"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\728c977381e74529572dc19bd646163f\transformed\jetified-transport-api-3.1.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\728c977381e74529572dc19bd646163f\transformed\jetified-transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d9b14d76205307403ed195f26fd0c15\transformed\jetified-firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d9b14d76205307403ed195f26fd0c15\transformed\jetified-firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:17.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e508e4bc1a2f9af16e1e03824e95753\transformed\jetified-firebase-components-17.1.5\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:17.1.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e508e4bc1a2f9af16e1e03824e95753\transformed\jetified-firebase-components-17.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"
      provided="true"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2f7e5a5b7eff93b63354f20751bf734\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2f7e5a5b7eff93b63354f20751bf734\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"
      provided="true"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"
      provided="true"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e1e09815d89e49b69a0dfa5f3213d7a\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e1e09815d89e49b69a0dfa5f3213d7a\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f73ff9b3bef37492e787482db1cabc0\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f73ff9b3bef37492e787482db1cabc0\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c23182fca5cd96e469b5cb76f079450\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c23182fca5cd96e469b5cb76f079450\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9952e16ecb7acd7f86cfecad684881f\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9952e16ecb7acd7f86cfecad684881f\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5224ccd3d5bb325ed8a7cc1db009055c\transformed\core-runtime-2.1.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5224ccd3d5bb325ed8a7cc1db009055c\transformed\core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"
      provided="true"/>
  <library
      name="androidx.annotation:annotation-jvm:1.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.6.0\a7257339a052df0f91433cf9651231bbb802b502\annotation-jvm-1.6.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.6.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10ec2b278a80aa9707f67a356c10ea22\transformed\jetified-annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10ec2b278a80aa9707f67a356c10ea22\transformed\jetified-annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.22\4dabb8248310d833bb6a8b516024a91fd3d275c\kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.8.22\636bf8b320e7627482771bbac9ed7246773c02bd\kotlin-stdlib-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.8.22"/>
  <library
      name="org.jetbrains:annotations:26.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\26.0.2\c7ce3cdeda3d18909368dfe5977332dfad326c6d\annotations-26.0.2.jar"
      resolved="org.jetbrains:annotations:26.0.2"/>
  <library
      name="com.github.QuadFlask:colorpicker:0.0.15@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a03e2d8ebbb816cc1056fc1d69f7bb1\transformed\jetified-colorpicker-0.0.15\jars\classes.jar"
      resolved="com.github.QuadFlask:colorpicker:0.0.15"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a03e2d8ebbb816cc1056fc1d69f7bb1\transformed\jetified-colorpicker-0.0.15"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.8.22\1a8e3601703ae14bb58757ea6b2d8e8e5935a586\kotlin-stdlib-common-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22"/>
  <library
      name="com.google.guava:guava:31.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\31.1-android\9222c47cc3ae890f07f7c961bbb3cb69050fe4aa\guava-31.1-android.jar"
      resolved="com.google.guava:guava:31.1-android"
      provided="true"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.test.espresso:espresso-idling-resource:3.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bebe14e82b55410b229ffae4023284a0\transformed\espresso-idling-resource-3.6.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-idling-resource:3.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bebe14e82b55410b229ffae4023284a0\transformed\espresso-idling-resource-3.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7dde31bcade14e0a8ec5ab233f207ab4\transformed\jetified-tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7dde31bcade14e0a8ec5ab233f207ab4\transformed\jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.15.0\38c8485a652f808c8c149150da4e5c2b0bd17f9a\error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"
      provided="true"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"
      provided="true"/>
  <library
      name="com.github.ozodrukh:CircularReveal:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5eb4c4c5fc3565a6abf8d37cdbd5a6\transformed\jetified-CircularReveal-1.1.1\jars\classes.jar"
      resolved="com.github.ozodrukh:CircularReveal:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5eb4c4c5fc3565a6abf8d37cdbd5a6\transformed\jetified-CircularReveal-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.nineoldandroids:library:2.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.nineoldandroids\library\2.4.0\e9b63380f3a242dbdbf103a2355ad7e43bad17cb\library-2.4.0.jar"
      resolved="com.nineoldandroids:library:2.4.0"
      provided="true"/>
  <library
      name="com.google.android.play:core-common:2.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\jars\classes.jar"
      resolved="com.google.android.play:core-common:2.0.3"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-solver\2.0.1\30988fe2d77f3fe3bf7551bb8a8b795fad7e7226\constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"
      provided="true"/>
  <library
      name="androidx.startup:startup-runtime:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f95d86ab7c9fdb42f31c7f87f6437bad\transformed\jetified-startup-runtime-1.0.0\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f95d86ab7c9fdb42f31c7f87f6437bad\transformed\jetified-startup-runtime-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"
      provided="true"/>
  <library
      name="org.checkerframework:checker-qual:3.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.12.0\d5692f0526415fcc6de94bb5bfbd3afd9dd3b3e5\checker-qual-3.12.0.jar"
      resolved="org.checkerframework:checker-qual:3.12.0"
      provided="true"/>
  <library
      name="com.google.j2objc:j2objc-annotations:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\1.3\ba035118bc8bac37d7eff77700720999acd9986d\j2objc-annotations-1.3.jar"
      resolved="com.google.j2objc:j2objc-annotations:1.3"
      provided="true"/>
</libraries>
