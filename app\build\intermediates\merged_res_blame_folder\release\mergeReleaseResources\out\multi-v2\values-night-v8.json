{"logs": [{"outputFile": "com.alwan.kids2025.app-mergeReleaseResources-56:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\36dc858f18a2a1e2b92bb4036b7b9aba\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4930,5005,5116,5205,5306,5413,5520,5619,5726,5829,5956,6044,6168,6270,6372,6488,6590,6704,6832,6948,7070,7206,7326,7460,7580,7692,7907,8024,8148,8278,8400,8538,8672,8788", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "5000,5111,5200,5301,5408,5515,5614,5721,5824,5951,6039,6163,6265,6367,6483,6585,6699,6827,6943,7065,7201,7321,7455,7575,7687,7813,8019,8143,8273,8395,8533,8667,8783,8903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40acce1c3a2b617941018f107a306df6\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "82,83,84,85,86,87,88,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4298,4368,4452,4536,4632,4734,4836,7818", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "4363,4447,4531,4627,4729,4831,4925,7902"}}, {"source": "Z:\\alwan6\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "22,3,43,37,38,5,21,42,46,20,23,29,30,28,25,14,16,15,13,17,34,39,33,48,4,10,8,9,24,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,86,1649,1415,1467,185,788,1601,1739,740,885,1109,1157,1060,981,496,603,551,447,657,1310,1513,1259,1833,137,361,265,312,931,1785", "endColumns": "47,50,46,51,45,49,48,47,45,47,45,47,53,48,46,54,53,51,48,48,54,48,50,47,47,49,46,48,49,47", "endOffsets": "880,132,1691,1462,1508,230,832,1644,1780,783,926,1152,1206,1104,1023,546,652,598,491,701,1360,1557,1305,1876,180,406,307,356,976,1828"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,103,154,201,253,299,349,398,446,492,540,586,634,688,737,784,839,893,945,994,1043,1098,1147,1198,1246,1294,1344,1391,1440,1490", "endColumns": "47,50,46,51,45,49,48,47,45,47,45,47,53,48,46,54,53,51,48,48,54,48,50,47,47,49,46,48,49,47", "endOffsets": "98,149,196,248,294,344,393,441,487,535,581,629,683,732,779,834,888,940,989,1038,1093,1142,1193,1241,1289,1339,1386,1435,1485,1533"}}, {"source": "Z:\\alwan6\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "3,55,40,33,62,48,26,19", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "79,2606,1965,1702,2867,2330,1358,1061", "endLines": "16,59,45,37,65,53,30,23", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "1018,2826,2294,1928,3034,2600,1667,1321"}, "to": {"startLines": "32,46,51,57,62,66,72,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1538,2482,2707,3041,3272,3444,3719,4033", "endLines": "45,50,56,61,65,71,76,81", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "2477,2702,3036,3267,3439,3714,4028,4293"}}]}]}