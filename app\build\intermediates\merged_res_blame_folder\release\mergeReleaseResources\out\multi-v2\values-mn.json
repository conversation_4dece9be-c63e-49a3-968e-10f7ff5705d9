{"logs": [{"outputFile": "com.alwan.kids2025.app-mergeReleaseResources-56:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4630b1237475cc69c83d8707fc0d9479\\transformed\\browser-1.4.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,264,369", "endColumns": "104,103,104,107", "endOffsets": "155,259,364,472"}, "to": {"startLines": "67,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6736,7155,7259,7364", "endColumns": "104,103,104,107", "endOffsets": "6836,7254,7359,7467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40acce1c3a2b617941018f107a306df6\\transformed\\appcompat-1.7.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,529,638,724,830,944,1027,1108,1199,1292,1387,1483,1580,1673,1767,1859,1950,2040,2120,2227,2330,2427,2534,2636,2749,2908,13723", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "424,524,633,719,825,939,1022,1103,1194,1287,1382,1478,1575,1668,1762,1854,1945,2035,2115,2222,2325,2422,2529,2631,2744,2903,3002,13799"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a28de868e92df6a446272321a9f2ec8d\\transformed\\jetified-play-services-base-18.0.1\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4451,4561,4718,4850,4957,5094,5220,5349,5609,5753,5860,6028,6157,6298,6466,6527,6589", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "4556,4713,4845,4952,5089,5215,5344,5454,5748,5855,6023,6152,6293,6461,6522,6584,6661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d1273d61679a02c43f1de22c3f405d6f\\transformed\\core-1.13.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3414,3512,3614,3715,3813,3918,4030,14049", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3507,3609,3710,3808,3913,4025,4144,14145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f1a8cebdf235588805d6f74854d945d7\\transformed\\preference-1.2.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,265,345,483,652,737", "endColumns": "69,89,79,137,168,84,81", "endOffsets": "170,260,340,478,647,732,814"}, "to": {"startLines": "66,70,138,147,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6666,6967,12414,13191,14150,14319,14404", "endColumns": "69,89,79,137,168,84,81", "endOffsets": "6731,7052,12489,13324,14314,14399,14481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\952446e2a2fa218bc85ba4e6563abcfd\\transformed\\jetified-play-services-ads-22.0.0\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,248,302,359,439,529,579,685,737,857,944,986,1065,1102,1139,1195,1274,1310", "endColumns": "48,53,56,79,89,49,105,51,119,86,41,78,36,36,55,78,35,55", "endOffsets": "247,301,358,438,528,578,684,736,856,943,985,1064,1101,1138,1194,1273,1309,1365"}, "to": {"startLines": "135,136,137,139,140,141,142,143,144,145,148,149,150,151,152,153,154,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12242,12295,12353,12494,12578,12672,12726,12836,12892,13016,13329,13375,13458,13499,13540,13600,13683,14486", "endColumns": "52,57,60,83,93,53,109,55,123,90,45,82,40,40,59,82,39,59", "endOffsets": "12290,12348,12409,12573,12667,12721,12831,12887,13011,13102,13370,13453,13494,13535,13595,13678,13718,14541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cbd131f1b1e0c9680dc8c1d103e011d9\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-mn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5459", "endColumns": "149", "endOffsets": "5604"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\36dc858f18a2a1e2b92bb4036b7b9aba\\transformed\\material-1.12.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,587,672,770,889,974,1035,1100,1198,1279,1338,1431,1493,1556,1614,1685,1747,1801,1922,1979,2040,2094,2165,2298,2382,2462,2558,2641,2724,2857,2939,3017,3149,3239,3319,3373,3424,3490,3561,3639,3710,3789,3864,3942,4022,4105,4210,4298,4377,4467,4560,4634,4704,4795,4849,4929,4996,5080,5165,5227,5291,5354,5425,5529,5644,5741,5855,5913,5968,6052,6139,6215", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,76,78,86,84,97,118,84,60,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,79,95,82,82,132,81,77,131,89,79,53,50,65,70,77,70,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83,86,75,81", "endOffsets": "260,339,416,495,582,667,765,884,969,1030,1095,1193,1274,1333,1426,1488,1551,1609,1680,1742,1796,1917,1974,2035,2089,2160,2293,2377,2457,2553,2636,2719,2852,2934,3012,3144,3234,3314,3368,3419,3485,3556,3634,3705,3784,3859,3937,4017,4100,4205,4293,4372,4462,4555,4629,4699,4790,4844,4924,4991,5075,5160,5222,5286,5349,5420,5524,5639,5736,5850,5908,5963,6047,6134,6210,6292"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,146,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3086,3163,3242,3329,4149,4247,4366,6841,6902,7057,7472,7553,7612,7705,7767,7830,7888,7959,8021,8075,8196,8253,8314,8368,8439,8572,8656,8736,8832,8915,8998,9131,9213,9291,9423,9513,9593,9647,9698,9764,9835,9913,9984,10063,10138,10216,10296,10379,10484,10572,10651,10741,10834,10908,10978,11069,11123,11203,11270,11354,11439,11501,11565,11628,11699,11803,11918,12015,12129,12187,13107,13804,13891,13967", "endLines": "5,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,146,156,157,158", "endColumns": "12,78,76,78,86,84,97,118,84,60,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,79,95,82,82,132,81,77,131,89,79,53,50,65,70,77,70,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83,86,75,81", "endOffsets": "310,3081,3158,3237,3324,3409,4242,4361,4446,6897,6962,7150,7548,7607,7700,7762,7825,7883,7954,8016,8070,8191,8248,8309,8363,8434,8567,8651,8731,8827,8910,8993,9126,9208,9286,9418,9508,9588,9642,9693,9759,9830,9908,9979,10058,10133,10211,10291,10374,10479,10567,10646,10736,10829,10903,10973,11064,11118,11198,11265,11349,11434,11496,11560,11623,11694,11798,11913,12010,12124,12182,12237,13186,13886,13962,14044"}}]}]}