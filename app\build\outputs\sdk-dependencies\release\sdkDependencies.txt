# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "26.0.2"
  }
  digests {
    sha256: " 7\2767\211\200\323\272\2233\351yU\363\262\315\343\222\252\022M\004\312s\316.\356fW\031\222\227"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.0"
  }
  digests {
    sha256: "\323\246vp\235\352\004\362\250Pn*\350PR\377\367c\333Rj\307\361k\004\336P\375\320[\a "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.8.22"
  }
  digests {
    sha256: "\003\245\303\226\\\303pQ\022\216d\344gH\343\224\266\275L\227\372\201\306\336o\307+\375D\343B\033"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.8.22"
  }
  digests {
    sha256: "\320\3026^$7\357p\363E\206\325\017\005WC\367\227\026\274\376e\344\274r9\315\322f\236\367\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.0"
  }
  digests {
    sha256: "\033\226\310\353\020\304\264\002\203\375\326\351\252t\377\377\005\372\344\361]T\366\033\246\235Q\177\315\024F\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.2"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.2"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.1"
  }
  digests {
    sha256: "\020s\023v\f\030\370\332\027N\215\201\003PJF\216\200n\210\367\265Z\204\275\034\016\256\352\021\216\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.1"
  }
  digests {
    sha256: "t\226\317\375\323\353\020\020\232\315\332\0342\022\366\254x\025x\236\t8\r\311\342\314\336\304\226\333\243\374"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.1"
  }
  digests {
    sha256: "\316\241\246\303\221\354Z8\362\365P\235\000l]\003k\355q\002\"u\341\366\247\256\334s^?8a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.1.0"
  }
  digests {
    sha256: "\326\005u\352\343\223P\346#HX\274\235}wSupz\350*hNl\257\177>A\241.%\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.4"
  }
  digests {
    sha256: "\274<$1\335\244.\224\273\225\021\305\207\352\350\220\322v\344\252\3769:\215\247\260\001i\030m\257\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.0"
  }
  digests {
    sha256: "Y\305Na)\351&\034m\201\017\216\352Jn\342\364\231M\201b\261\315\237\310\214\234\204\311*\314\374"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.2"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.2"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\237,\026\343:U\272\215g\243b:U\276\377\362\354\200d>?\n\222g`\337\035\225|?\212\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.2"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "(\240\2704\364\016\335R\342\370\001\v\3340W\322\240\314v\252\245\254\223\021\255\260\271\316\221\234\251\314"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.6.2"
  }
  digests {
    sha256: "\212\316\2311?\n\346\257G\031K\312\376(\363D\343c\364\322\223\370K+\227\264\201s[\004\336\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.2"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\177\300\372#J3!\261\363M\265\206+\027\332\203\321\306,\033\306n\302\375O\033\260\247q\254\372\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.2"
  }
  digests {
    sha256: "{\307\334\272\261v6\354\ao\022\257\344\320&q&\\8\224W\261\263f\263z\016\214\271\036-\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.0"
  }
  digests {
    sha256: "\277\356\022\301\310\214?t\225O\277ngf\274\0309V\363tx\267\300$\372\347\365\263\204\223\327\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-v4"
    version: "1.0.0"
  }
  digests {
    sha256: "x\376\301H_\0178\212GI\002-\325\024\026\205q\'\315%D\256\034?\320\261e\211\005T\200\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.0.0"
  }
  digests {
    sha256: "\262;R{+\254\207\fJtQ\346\230-q2\344\023\350\215\177\'\333\353\037\307d\nr\f\331\356"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.0.1"
  }
  digests {
    sha256: "\354\025\265\324\242\357\360x\210\274\024\231\316.,n\376$\300\355`\314W\260\214\235\304\266\375<!\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-solver"
    version: "2.0.1"
  }
  digests {
    sha256: "\26272\355\2735\021\3317\376\241\377\357\004{\016l\000\033P\301\222\037\r\225\237\303\204\327\006\354j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.ViksaaSkool"
    artifactId: "AwesomeSplash"
    version: "v1.0.0"
  }
  digests {
    sha256: "\236\177\317\024d\316I\306\240\360\367\327\311+M\202\361\323\005J5\307\031J\2259\305\326\246t\230\356"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.daimajia.androidanimations"
    artifactId: "library"
    version: "1.1.3"
  }
  digests {
    sha256: "\000p\237\203\2163\375\317E\336\301Z\033\235f#\343=MP\255\t\312\221iYb\215`\273\354*"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.nineoldandroids"
    artifactId: "library"
    version: "2.4.0"
  }
  digests {
    sha256: "h\002Z\024\343\347g=j\322\371^KF\327\215}\006\203C\252\231%kho\345\235\341\263\026:"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.daimajia.easing"
    artifactId: "library"
    version: "1.0.1"
  }
  digests {
    sha256: ":\b\344]3\366\336\212\2215\r]\334\320.\\+_#}t\375\353$\266\0029c\222+\267x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.ozodrukh"
    artifactId: "CircularReveal"
    version: "1.1.1"
  }
  digests {
    sha256: "\203\377G\336\376.\v\262\f\373\200!\214\271w\300,\026\a\255\'Il\1776\343\232\216v}\337L"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.github.jorgecastilloprz"
    artifactId: "fillableloaders"
    version: "1.02"
  }
  digests {
    sha256: "t\222P\032:c\377\201\340\372\016\350!:\353 n|\270\331\333\210\001a\303\310\356\255\362\263\364\355"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads"
    version: "22.0.0"
  }
  digests {
    sha256: "\333\206)\320\307q\306\260\365\252|\031\347W\342-\231T\337\261\376D\355\210`\333\021\020\224z\201\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.4.0"
  }
  digests {
    sha256: "\341\220o\204Q/\036\245\344\311&\333\271\025x\232\330\367IO\244\356\232\322E\026?v\030\\\354\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-base"
    version: "22.0.0"
  }
  digests {
    sha256: "\177.C\214>\f]W\372\275\234k\202\036\204\302|\352\302%F\317l\306B\364\216\377\353\000\266\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-lite"
    version: "22.0.0"
  }
  digests {
    sha256: "\v\312pI\363\230\213=\371I\177\307\323Q:\222{\\\033\331P\266N\032|\222\351\244\263 \221q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\210\202>\000\272\232\256m\306Cj\355Y(\300p\326\212b\302X\035\351ne\\\250o\003\016r\251"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.2.5"
  }
  digests {
    sha256: "$\245T\233ynC\3437Q=)\b\255\254g\364SP\331\251\v\312~.a i!@\273\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.2.5"
  }
  digests {
    sha256: "+\023\r\324\241\323\331\033g\001\3553\tm8\237\001\304\374\021\227\247\254\326\271\027$\335\305\254\374\006"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.1.0"
  }
  digests {
    sha256: "\206ss\177\333.\373\255\221\256\256\355\031\'\353\262\222\022\323j\206}\223\271c\234\200i\001\237\212\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.1.0"
  }
  digests {
    sha256: "\203A\377\t-``\326*\a\"\177)#qU\377\363o\261o\226\311_\275\232\210N7]\271\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "21.6.2"
  }
  digests {
    sha256: "\000\330\035\375a\350:4\353Q!r\246%\362\254\314Q\354\301\341\247\"\202\251g\222Z\341\363\360k"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "21.6.2"
  }
  digests {
    sha256: "\027\373\301\265\201\264w\327&\a\233\337\271\274y\232\253Ni\210y\027wv\322\306\216\322\330k\267\254"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.ump"
    artifactId: "user-messaging-platform"
    version: "3.2.0"
  }
  digests {
    sha256: "\027\262\303\364Th\313\2057\246d\353x8\\\006\221\027\212\2534\020,\246\350U\220(\037!&&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-appset"
    version: "16.0.1"
  }
  digests {
    sha256: "\340N\315Ou\317E\312,7\273S\373 \224Z\234Z2,\025\341l$\031pmg/\260\020\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.0.1"
  }
  digests {
    sha256: "(\226\327oC+\345!g)[\271\316E\255\342\\1\n\357\374\004\322\214\370\333j\025\206\216\203\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "32.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "21.6.2"
  }
  digests {
    sha256: "\354\233<\2069\027\227UN\326=P-\222\337\241\266\251\212\273\266!\303\336\254Js!\317\262\273,"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "21.6.2"
  }
  digests {
    sha256: "\371\a\307\336[\357\240V\364\353\342\377r\344\202;\312\207\265\r>\347dY!X\331\233[\025\276*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "21.6.2"
  }
  digests {
    sha256: "\254\354\314\325G\304d\231\220\205\306{q\231B\231AF\333\306\343\2703:E\234\271\031\234\214\254\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\001\'W\214\334\355\255|\216\316\027H\024\306\364\343\002x\216\217{\321N\203=\221\234\033vY\331}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\227\352S+F\274\203\365\254\344\a\342B\247\254\315\330+\204\2407u\331n\257\\\341`\316\275\273\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "21.6.2"
  }
  digests {
    sha256: "JL=\246O\333\276\311\342\334\240\271S\232\337\237\377\365/ \370\216\310#\226\257\303\346\031\356\201\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "20.4.3"
  }
  digests {
    sha256: "ww1qI\334\b9z\203`-\373R~\317\211(\225C\357\032\274\335V\336}DM\252\337\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "17.1.5"
  }
  digests {
    sha256: "\344\3013\370\005[\030\224\201J\321i\002\216\027\366\262\024\203\215\vg\207&\215\316\375\300\235N\235\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "20.4.3"
  }
  digests {
    sha256: "\325\253 &2M\0378+\277\304e\215\037\217d\n\352\337[\341+:\037\a\352{M$a<\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "17.2.0"
  }
  digests {
    sha256: "\336\001\036j;ya\336c\217\027/,\266k\243\004\352\251\211f*\236+\017H\337\367t\314\301f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "21.6.2"
  }
  digests {
    sha256: "%bvp\032\241\373V\313\343E\253\006\"\243\313=G\231m\241\363\236\305kzf\274k\"\322+"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "23.4.1"
  }
  digests {
    sha256: "\025\302c|(\373h\225\266\244\311\230\241\221\266c\363\267\212\310\025\366\254Y\253}g:\215\232\362\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.1.0"
  }
  digests {
    sha256: "}\257\303\237\016\2505G3f\254\303F\367\346\177\310D?Ld]\231\234>\230{\312\326\270\214{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.8"
  }
  digests {
    sha256: "\341~\335\036\367\375G\\\220\272\244\343\224\"3/\'\b}4\274\264l\264\214\350j\371\245Ja."
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.8"
  }
  digests {
    sha256: "\313\223S\357\027\221\256\027\t}\207\214\247\021\342Z\2342\316\311\004*\334I\260\f\255\376\341\247)\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.1.0"
  }
  digests {
    sha256: "\362\005 f\307L\203\032\343V\263t\"|\273\237\351\304\207~\252Z\032\311\032W$U\240\363F\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.1.7"
  }
  digests {
    sha256: "_#\325u\n\342H\334\2421,Yc\252\211\370Q\2766\023/\270\237\025\021L5\205\346\005\377\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.QuadFlask"
    artifactId: "colorpicker"
    version: "0.0.15"
  }
  digests {
    sha256: "\023N>y\364@\\\033L\376\333\262\002\317\221\037\366\005w\360J\231d\020\303kA\247m\305\273*"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "review"
    version: "2.0.1"
  }
  digests {
    sha256: "&\234bAe\207V\355\356\345\231=\264Wq\231\330\006\253\022R\376t\342\r\002A\207\212H6\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "core-common"
    version: "2.0.3"
  }
  digests {
    sha256: "C\003%_\025,Y\271T\221\233\264q\321N\345\376\322\337x5e\336\250R\213\b\b;\2474+"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "app-update"
    version: "2.1.0"
  }
  digests {
    sha256: "\025e\351\357\001O|\267\031?\004b\344\330\027H\n\377K3?\307\367\340\005+\2712\314(\202\r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.1"
  }
  digests {
    sha256: "@\312\212\337\333~\377\266\037\254\263\233\331\312./:@\321\006t;\f\326\334\236!\350\276\335O\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.3.6"
  }
  digests {
    sha256: "?\204\240\023\375\353\213\254\222\324\253`z\353\363\232O\371E\364XZcY`\355v\234\320%]\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.onesignal"
    artifactId: "OneSignal"
    version: "4.8.6"
  }
  digests {
    sha256: "e\r\235\337(\2174[\2146\266\245\236\362\262\200\a\n\324!\322\351\364\245\234\234\221\272\t\021ys"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 47
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 25
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 24
  library_dep_index: 13
  library_dep_index: 34
  library_dep_index: 44
  library_dep_index: 54
  library_dep_index: 37
  library_dep_index: 5
  library_dep_index: 47
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 44
  library_dep_index: 37
  library_dep_index: 31
  library_dep_index: 5
  library_dep_index: 46
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 4
  library_dep_index: 5
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 0
}
library_dependencies {
  library_index: 7
  library_dep_index: 3
}
library_dependencies {
  library_index: 8
  library_dep_index: 3
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 45
  library_dep_index: 5
  library_dep_index: 25
}
library_dependencies {
  library_index: 9
  library_dep_index: 5
}
library_dependencies {
  library_index: 10
  library_dep_index: 3
  library_dep_index: 11
}
library_dependencies {
  library_index: 12
  library_dep_index: 3
}
library_dependencies {
  library_index: 13
  library_dep_index: 3
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 44
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 27
  library_dep_index: 33
}
library_dependencies {
  library_index: 14
  library_dep_index: 3
}
library_dependencies {
  library_index: 15
  library_dep_index: 3
  library_dep_index: 14
}
library_dependencies {
  library_index: 16
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 17
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 42
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 19
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 6
  library_dep_index: 42
}
library_dependencies {
  library_index: 20
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 18
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 42
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 39
  library_dep_index: 37
  library_dep_index: 40
  library_dep_index: 5
}
library_dependencies {
  library_index: 25
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 5
  library_dep_index: 8
}
library_dependencies {
  library_index: 26
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 27
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 26
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 28
  library_dep_index: 26
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 29
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 30
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 30
  library_dep_index: 3
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 3
}
library_dependencies {
  library_index: 32
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 5
  library_dep_index: 17
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 33
  library_dep_index: 13
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 34
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 35
  library_dep_index: 34
  library_dep_index: 5
  library_dep_index: 17
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 3
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 34
  library_dep_index: 37
  library_dep_index: 5
  library_dep_index: 17
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 37
  library_dep_index: 3
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 5
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 37
  library_dep_index: 5
  library_dep_index: 37
}
library_dependencies {
  library_index: 39
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 27
  library_dep_index: 34
}
library_dependencies {
  library_index: 40
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 7
}
library_dependencies {
  library_index: 42
  library_dep_index: 5
  library_dep_index: 43
}
library_dependencies {
  library_index: 43
  library_dep_index: 5
}
library_dependencies {
  library_index: 44
  library_dep_index: 3
  library_dep_index: 10
  library_dep_index: 30
  library_dep_index: 11
}
library_dependencies {
  library_index: 45
  library_dep_index: 3
  library_dep_index: 7
}
library_dependencies {
  library_index: 46
  library_dep_index: 2
  library_dep_index: 25
  library_dep_index: 32
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 5
  library_dep_index: 2
}
library_dependencies {
  library_index: 47
  library_dep_index: 3
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 48
  library_dep_index: 49
  library_dep_index: 1
}
library_dependencies {
  library_index: 48
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 7
}
library_dependencies {
  library_index: 49
  library_dep_index: 48
  library_dep_index: 12
  library_dep_index: 7
}
library_dependencies {
  library_index: 50
  library_dep_index: 3
}
library_dependencies {
  library_index: 51
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 41
}
library_dependencies {
  library_index: 52
  library_dep_index: 3
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 52
  library_dep_index: 52
}
library_dependencies {
  library_index: 54
  library_dep_index: 3
}
library_dependencies {
  library_index: 55
  library_dep_index: 8
  library_dep_index: 56
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 24
}
library_dependencies {
  library_index: 56
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 45
}
library_dependencies {
  library_index: 57
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 58
  library_dep_index: 39
  library_dep_index: 59
  library_dep_index: 60
}
library_dependencies {
  library_index: 58
  library_dep_index: 3
}
library_dependencies {
  library_index: 59
  library_dep_index: 3
}
library_dependencies {
  library_index: 60
  library_dep_index: 3
}
library_dependencies {
  library_index: 61
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 57
  library_dep_index: 41
  library_dep_index: 40
  library_dep_index: 62
  library_dep_index: 51
  library_dep_index: 63
  library_dep_index: 12
  library_dep_index: 67
  library_dep_index: 68
  library_dep_index: 50
}
library_dependencies {
  library_index: 62
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 41
  library_dep_index: 7
}
library_dependencies {
  library_index: 63
  library_dep_index: 3
  library_dep_index: 41
  library_dep_index: 8
  library_dep_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 64
  library_dep_index: 5
  library_dep_index: 17
  library_dep_index: 3
  library_dep_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 65
  library_dep_index: 3
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 66
}
library_dependencies {
  library_index: 66
  library_dep_index: 8
  library_dep_index: 7
  library_dep_index: 57
}
library_dependencies {
  library_index: 67
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 12
}
library_dependencies {
  library_index: 68
  library_dep_index: 3
  library_dep_index: 8
}
library_dependencies {
  library_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 1
  library_dep_index: 72
  library_dep_index: 62
  library_dep_index: 73
  library_dep_index: 8
  library_dep_index: 51
  library_dep_index: 66
  library_dep_index: 9
  library_dep_index: 24
  library_dep_index: 13
  library_dep_index: 75
  library_dep_index: 54
  library_dep_index: 65
  library_dep_index: 48
  library_dep_index: 76
}
library_dependencies {
  library_index: 70
  library_dep_index: 5
  library_dep_index: 43
  library_dep_index: 6
  library_dep_index: 42
}
library_dependencies {
  library_index: 72
  library_dep_index: 3
}
library_dependencies {
  library_index: 73
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 74
}
library_dependencies {
  library_index: 75
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 41
  library_dep_index: 7
}
library_dependencies {
  library_index: 76
  library_dep_index: 3
  library_dep_index: 24
  library_dep_index: 75
  library_dep_index: 8
  library_dep_index: 7
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 81
  library_dep_index: 80
  library_dep_index: 82
  library_dep_index: 1
}
library_dependencies {
  library_index: 78
  library_dep_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 79
  library_dep_index: 1
}
library_dependencies {
  library_index: 81
  library_dep_index: 79
}
library_dependencies {
  library_index: 82
  library_dep_index: 55
}
library_dependencies {
  library_index: 83
  library_dep_index: 84
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 85
  library_dep_index: 86
  library_dep_index: 95
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 84
  library_dep_index: 7
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 8
  library_dep_index: 3
  library_dep_index: 11
}
library_dependencies {
  library_index: 85
  library_dep_index: 23
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
  library_dep_index: 85
  library_dep_index: 23
  library_dep_index: 92
  library_dep_index: 94
}
library_dependencies {
  library_index: 87
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 30
  library_dep_index: 8
  library_dep_index: 88
  library_dep_index: 91
  library_dep_index: 90
  library_dep_index: 8
  library_dep_index: 33
}
library_dependencies {
  library_index: 88
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 15
}
library_dependencies {
  library_index: 89
  library_dep_index: 3
}
library_dependencies {
  library_index: 90
  library_dep_index: 3
  library_dep_index: 91
}
library_dependencies {
  library_index: 91
  library_dep_index: 3
}
library_dependencies {
  library_index: 92
  library_dep_index: 23
  library_dep_index: 93
}
library_dependencies {
  library_index: 93
  library_dep_index: 23
}
library_dependencies {
  library_index: 94
  library_dep_index: 3
  library_dep_index: 23
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 96
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 97
  library_dep_index: 98
  library_dep_index: 120
  library_dep_index: 122
  library_dep_index: 111
  library_dep_index: 115
  library_dep_index: 116
}
library_dependencies {
  library_index: 98
  library_dep_index: 99
  library_dep_index: 110
  library_dep_index: 119
}
library_dependencies {
  library_index: 99
  library_dep_index: 7
  library_dep_index: 57
  library_dep_index: 100
  library_dep_index: 23
  library_dep_index: 93
  library_dep_index: 101
  library_dep_index: 109
}
library_dependencies {
  library_index: 100
  library_dep_index: 23
}
library_dependencies {
  library_index: 101
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 100
  library_dep_index: 23
  library_dep_index: 93
  library_dep_index: 109
  library_dep_index: 104
}
library_dependencies {
  library_index: 102
  library_dep_index: 3
  library_dep_index: 25
  library_dep_index: 5
  library_dep_index: 18
  library_dep_index: 103
}
library_dependencies {
  library_index: 103
  library_dep_index: 3
  library_dep_index: 10
  library_dep_index: 25
  library_dep_index: 102
  library_dep_index: 104
  library_dep_index: 11
  library_dep_index: 5
  library_dep_index: 18
  library_dep_index: 102
}
library_dependencies {
  library_index: 104
  library_dep_index: 105
  library_dep_index: 11
  library_dep_index: 106
  library_dep_index: 107
  library_dep_index: 71
  library_dep_index: 108
}
library_dependencies {
  library_index: 109
  library_dep_index: 57
  library_dep_index: 23
}
library_dependencies {
  library_index: 110
  library_dep_index: 100
  library_dep_index: 23
  library_dep_index: 93
  library_dep_index: 92
  library_dep_index: 22
  library_dep_index: 111
  library_dep_index: 115
  library_dep_index: 112
  library_dep_index: 116
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 104
  library_dep_index: 5
}
library_dependencies {
  library_index: 111
  library_dep_index: 21
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 3
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 112
  library_dep_index: 113
  library_dep_index: 3
  library_dep_index: 71
}
library_dependencies {
  library_index: 113
  library_dep_index: 114
}
library_dependencies {
  library_index: 115
  library_dep_index: 111
  library_dep_index: 42
  library_dep_index: 112
  library_dep_index: 113
}
library_dependencies {
  library_index: 116
  library_dep_index: 117
  library_dep_index: 5
  library_dep_index: 22
  library_dep_index: 113
  library_dep_index: 111
  library_dep_index: 115
  library_dep_index: 112
}
library_dependencies {
  library_index: 117
  library_dep_index: 22
  library_dep_index: 113
}
library_dependencies {
  library_index: 118
  library_dep_index: 23
  library_dep_index: 113
}
library_dependencies {
  library_index: 119
  library_dep_index: 7
  library_dep_index: 23
  library_dep_index: 93
  library_dep_index: 101
}
library_dependencies {
  library_index: 120
  library_dep_index: 23
  library_dep_index: 22
  library_dep_index: 71
  library_dep_index: 121
  library_dep_index: 123
  library_dep_index: 5
  library_dep_index: 3
  library_dep_index: 124
  library_dep_index: 125
  library_dep_index: 126
  library_dep_index: 96
  library_dep_index: 128
  library_dep_index: 109
  library_dep_index: 127
  library_dep_index: 122
  library_dep_index: 129
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 111
  library_dep_index: 115
  library_dep_index: 112
  library_dep_index: 116
}
library_dependencies {
  library_index: 121
  library_dep_index: 3
  library_dep_index: 122
}
library_dependencies {
  library_index: 122
  library_dep_index: 3
}
library_dependencies {
  library_index: 123
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 124
  library_dep_index: 3
}
library_dependencies {
  library_index: 125
  library_dep_index: 3
  library_dep_index: 124
  library_dep_index: 126
  library_dep_index: 122
  library_dep_index: 127
}
library_dependencies {
  library_index: 126
  library_dep_index: 3
  library_dep_index: 124
  library_dep_index: 122
  library_dep_index: 121
  library_dep_index: 114
}
library_dependencies {
  library_index: 127
  library_dep_index: 3
  library_dep_index: 122
}
library_dependencies {
  library_index: 128
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 129
  library_dep_index: 3
  library_dep_index: 124
  library_dep_index: 125
  library_dep_index: 126
}
library_dependencies {
  library_index: 130
  library_dep_index: 1
}
library_dependencies {
  library_index: 131
  library_dep_index: 23
  library_dep_index: 22
  library_dep_index: 132
}
library_dependencies {
  library_index: 133
  library_dep_index: 23
  library_dep_index: 22
  library_dep_index: 132
}
library_dependencies {
  library_index: 134
  library_dep_index: 3
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 46
  library_dep_index: 135
  library_dep_index: 75
  library_dep_index: 63
  library_dep_index: 7
}
library_dependencies {
  library_index: 135
  library_dep_index: 24
  library_dep_index: 46
  library_dep_index: 25
  library_dep_index: 136
  library_dep_index: 28
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 5
}
library_dependencies {
  library_index: 136
  library_dep_index: 5
  library_dep_index: 7
}
library_dependencies {
  library_index: 137
  library_dep_index: 120
  library_dep_index: 72
  library_dep_index: 55
  library_dep_index: 84
  library_dep_index: 1
  library_dep_index: 87
  library_dep_index: 43
  library_dep_index: 96
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 1
  dependency_index: 55
  dependency_index: 69
  dependency_index: 72
  dependency_index: 77
  dependency_index: 83
  dependency_index: 94
  dependency_index: 97
  dependency_index: 120
  dependency_index: 98
  dependency_index: 130
  dependency_index: 131
  dependency_index: 133
  dependency_index: 134
  dependency_index: 25
  dependency_index: 137
  dependency_index: 8
  dependency_index: 2
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
