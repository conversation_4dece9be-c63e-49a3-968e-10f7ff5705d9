package com.alwan.kids2025;

import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Typeface;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;
import com.google.android.ump.ConsentForm;
import com.google.android.ump.ConsentInformation;

/**
 * New HomeActivity that integrates categories directly into the home page
 * Replaces the separate Categories activity for better user experience
 */
public class HomeActivity extends BaseLocalizedActivity implements View.OnClickListener {
    private static final String TAG = "HomeActivity";
    
    // Category views
    ImageView c1, c2, c3, c4, c5, c6;
    TextView tv1, tv2, tv3, tv4, tv5, tv6;
    TextView welcomeText;
    
    // Ad and preferences
    public static AdView mAdView;
    SharedPreferences preferences;
    private ConsentInformation consentInformation;
    private ConsentForm consentForm;
    
    int selectedCategoryCode;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home);

        // Initialize preferences
        preferences = PreferenceManager.getDefaultSharedPreferences(this);

        // Initialize AdMob
        initializeAds();

        // Initialize views
        initializeViews();

        // Setup fonts
        setupFonts();

        // Setup click listeners
        setupClickListeners();

        // Setup bottom navigation - Gallery tab (Categories is now home)
        BottomNavigationHelperSimple.setupBottomNavigation(this, BottomNavigationHelperSimple.TAB_GALLERY);
    }

    private void initializeAds() {
        MobileAds.initialize(this, new OnInitializationCompleteListener() {
            @Override
            public void onInitializationComplete(InitializationStatus initializationStatus) {
                // Initialization complete
            }
        });

        mAdView = findViewById(R.id.adView);
        if (mAdView != null) {
            AdRequest adRequest = new AdRequest.Builder().build();
            mAdView.loadAd(adRequest);
        }
    }

    private void initializeViews() {
        // Welcome text
        welcomeText = findViewById(R.id.welcome_text);
        
        // Category images
        c1 = findViewById(R.id.c_1);
        c2 = findViewById(R.id.c_2);
        c3 = findViewById(R.id.c_3);
        c4 = findViewById(R.id.c_4);
        c5 = findViewById(R.id.c_5);
        c6 = findViewById(R.id.c_6);

        // Category text views
        tv1 = findViewById(R.id.tv_1);
        tv2 = findViewById(R.id.tv_2);
        tv3 = findViewById(R.id.tv_3);
        tv4 = findViewById(R.id.tv_4);
        tv5 = findViewById(R.id.tv_5);
        tv6 = findViewById(R.id.tv_6);
    }

    private void setupFonts() {
        // Set Arabic font for all text views
        Typeface tf = Typeface.createFromAsset(getAssets(), "fonts/Blabeloo.ttf");
        
        if (welcomeText != null) welcomeText.setTypeface(tf);
        if (tv1 != null) tv1.setTypeface(tf);
        if (tv2 != null) tv2.setTypeface(tf);
        if (tv3 != null) tv3.setTypeface(tf);
        if (tv4 != null) tv4.setTypeface(tf);
        if (tv5 != null) tv5.setTypeface(tf);
        if (tv6 != null) tv6.setTypeface(tf);
    }

    private void setupClickListeners() {
        if (c1 != null) c1.setOnClickListener(this);
        if (c2 != null) c2.setOnClickListener(this);
        if (c3 != null) c3.setOnClickListener(this);
        if (c4 != null) c4.setOnClickListener(this);
        if (c5 != null) c5.setOnClickListener(this);
        if (c6 != null) c6.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.c_1:
                selectedCategoryCode = 1;
                break;
            case R.id.c_2:
                selectedCategoryCode = 2;
                break;
            case R.id.c_3:
                selectedCategoryCode = 3;
                break;
            case R.id.c_4:
                selectedCategoryCode = 4;
                break;
            case R.id.c_5:
                selectedCategoryCode = 5;
                break;
            case R.id.c_6:
                selectedCategoryCode = 6;
                break;
            default:
                return;
        }

        // Navigate to categories page
        Intent intent = new Intent(HomeActivity.this, Categories.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra("code", selectedCategoryCode);
        startActivity(intent);
    }

    @Override
    protected boolean shouldShowExitDialog() {
        return true; // Show exit dialog for home activity
    }

    protected boolean useToolbar() {
        return false; // No toolbar for home activity
    }

    protected boolean useDrawerToggle() {
        return false;
    }
}
