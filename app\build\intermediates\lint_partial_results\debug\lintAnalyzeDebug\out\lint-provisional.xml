<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.1" type="conditional_incidents">

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE, android.permission.WAKE_LOCK" message="Missing permissions required by FirebaseAnalytics.getInstance: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MainActivity.java"
            line="112"
            column="30"
            startOffset="4155"
            endLine="112"
            endColumn="65"
            endOffset="4190"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by FirebaseAnalytics.getInstance: %1$s"/>
            <entry
                name="requirement"
                string="&amp;android.permission.INTERNET,android.permission.ACCESS_NETWORK_STATE,android.permission.WAKE_LOCK"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="23-∞" requiresApi="27-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="16"
            column="15"
            startOffset="953"
            endLine="16"
            endColumn="54"
            endOffset="992"/>
        <map>
            <entry
                name="message"
                string="`android:windowLightNavigationBar` requires API level 27 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="27-∞"/>
        </map>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/alwan/kids2025/MyFirebaseMessagingService.java"
            line="66"
            column="9"
            startOffset="2698"
            endLine="66"
            endColumn="67"
            endOffset="2756"/>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="63"
            column="17"
            startOffset="2625"
            endLine="63"
            endColumn="51"
            endOffset="2659"/>
        <map>
            <entry
                name="message"
                string="Attribute `autoSizeTextType` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="64"
            column="17"
            startOffset="2677"
            endLine="64"
            endColumn="51"
            endOffset="2711"/>
        <map>
            <entry
                name="message"
                string="Attribute `autoSizeMinTextSize` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="65"
            column="17"
            startOffset="2729"
            endLine="65"
            endColumn="51"
            endOffset="2763"/>
        <map>
            <entry
                name="message"
                string="Attribute `autoSizeMaxTextSize` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="66"
            column="17"
            startOffset="2781"
            endLine="66"
            endColumn="54"
            endOffset="2818"/>
        <map>
            <entry
                name="message"
                string="Attribute `autoSizeStepGranularity` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="81"
            column="17"
            startOffset="3419"
            endLine="81"
            endColumn="51"
            endOffset="3453"/>
        <map>
            <entry
                name="message"
                string="Attribute `autoSizeTextType` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="82"
            column="17"
            startOffset="3471"
            endLine="82"
            endColumn="51"
            endOffset="3505"/>
        <map>
            <entry
                name="message"
                string="Attribute `autoSizeMinTextSize` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="83"
            column="17"
            startOffset="3523"
            endLine="83"
            endColumn="51"
            endOffset="3557"/>
        <map>
            <entry
                name="message"
                string="Attribute `autoSizeMaxTextSize` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_categories.xml"
            line="84"
            column="17"
            startOffset="3575"
            endLine="84"
            endColumn="54"
            endOffset="3612"/>
        <map>
            <entry
                name="message"
                string="Attribute `autoSizeStepGranularity` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/font/blabeloo.xml"
            line="4"
            column="9"
            startOffset="130"
            endLine="4"
            endColumn="35"
            endOffset="156"/>
        <map>
            <entry
                name="message"
                string="Attribute `fontStyle` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/font/blabeloo.xml"
            line="5"
            column="9"
            startOffset="165"
            endLine="5"
            endColumn="33"
            endOffset="189"/>
        <map>
            <entry
                name="message"
                string="Attribute `fontWeight` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/font/blabeloo.xml"
            line="6"
            column="9"
            startOffset="198"
            endLine="6"
            endColumn="46"
            endOffset="235"/>
        <map>
            <entry
                name="message"
                string="Attribute `font` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_ads_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="47"
            endOffset="277"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_animals_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_app_logo.xml"
            line="6"
            column="19"
            startOffset="201"
            endLine="6"
            endColumn="40"
            endOffset="222"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_app_logo.xml"
            line="8"
            column="26"
            startOffset="258"
            endLine="8"
            endColumn="46"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_forward.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cartoons_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_categories_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_clear_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_dark_mode.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_delete.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_delete_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_display_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_empty_gallery.xml"
            line="8"
            column="28"
            startOffset="260"
            endLine="8"
            endColumn="49"
            endOffset="281"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_empty_heart.xml"
            line="8"
            column="28"
            startOffset="260"
            endLine="8"
            endColumn="49"
            endOffset="281"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_exit_app.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_export_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_favorites_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_flowers_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_foods_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="46"
            endOffset="276"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_gallery_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_home_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image_quality.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_info.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_info_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_language.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_more_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nature_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="47"
            endOffset="277"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notifications.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_privacy_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="47"
            endOffset="277"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_refresh.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_refresh_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_save.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_share_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_star_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_storage.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_support_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="50"
            endOffset="280"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_tip_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_transport_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="50"
            endOffset="280"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_vibration.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_volume_modern.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_volume_up.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="48"
            endOffset="278"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="74"
            column="37"
            startOffset="3274"
            endLine="74"
            endColumn="57"
            endOffset="3294"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="74"
            column="37"
            startOffset="3411"
            endLine="74"
            endColumn="57"
            endOffset="3431"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="75"
            column="37"
            startOffset="3339"
            endLine="75"
            endColumn="57"
            endOffset="3359"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="131"
            column="37"
            startOffset="6063"
            endLine="131"
            endColumn="57"
            endOffset="6083"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="133"
            column="37"
            startOffset="6193"
            endLine="133"
            endColumn="57"
            endOffset="6213"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="133"
            column="37"
            startOffset="6325"
            endLine="133"
            endColumn="57"
            endOffset="6345"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="192"
            column="37"
            startOffset="9239"
            endLine="192"
            endColumn="57"
            endOffset="9259"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="198"
            column="37"
            startOffset="9203"
            endLine="198"
            endColumn="57"
            endOffset="9223"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="202"
            column="37"
            startOffset="9399"
            endLine="202"
            endColumn="57"
            endOffset="9419"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="255"
            column="37"
            startOffset="11993"
            endLine="255"
            endColumn="57"
            endOffset="12013"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="260"
            column="37"
            startOffset="12254"
            endLine="260"
            endColumn="57"
            endOffset="12274"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="260"
            column="37"
            startOffset="12516"
            endLine="260"
            endColumn="57"
            endOffset="12536"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="318"
            column="37"
            startOffset="15429"
            endLine="318"
            endColumn="57"
            endOffset="15449"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="323"
            column="37"
            startOffset="15138"
            endLine="323"
            endColumn="57"
            endOffset="15158"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="329"
            column="37"
            startOffset="15464"
            endLine="329"
            endColumn="57"
            endOffset="15484"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="64"
            column="57"
            startOffset="2837"
            endLine="64"
            endColumn="68"
            endOffset="2848"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="65"
            column="57"
            startOffset="2775"
            endLine="65"
            endColumn="68"
            endOffset="2786"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="65"
            column="57"
            startOffset="2775"
            endLine="65"
            endColumn="68"
            endOffset="2786"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="122"
            column="57"
            startOffset="5564"
            endLine="122"
            endColumn="68"
            endOffset="5575"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="123"
            column="57"
            startOffset="5629"
            endLine="123"
            endColumn="68"
            endOffset="5640"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="123"
            column="57"
            startOffset="5751"
            endLine="123"
            endColumn="68"
            endOffset="5762"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="182"
            column="57"
            startOffset="8665"
            endLine="182"
            endColumn="68"
            endOffset="8676"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="189"
            column="57"
            startOffset="8704"
            endLine="189"
            endColumn="68"
            endOffset="8715"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="192"
            column="57"
            startOffset="8835"
            endLine="192"
            endColumn="68"
            endOffset="8846"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="246"
            column="57"
            startOffset="11494"
            endLine="246"
            endColumn="68"
            endOffset="11505"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="250"
            column="57"
            startOffset="11690"
            endLine="250"
            endColumn="68"
            endOffset="11701"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="250"
            column="57"
            startOffset="11942"
            endLine="250"
            endColumn="68"
            endOffset="11953"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="308"
            column="57"
            startOffset="14855"
            endLine="308"
            endColumn="68"
            endOffset="14866"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="314"
            column="57"
            startOffset="14639"
            endLine="314"
            endColumn="68"
            endOffset="14650"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="319"
            column="57"
            startOffset="14900"
            endLine="319"
            endColumn="68"
            endOffset="14911"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-port/activity_categories.xml"
            line="366"
            column="57"
            startOffset="17765"
            endLine="366"
            endColumn="68"
            endOffset="17776"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-land/activity_categories.xml"
            line="372"
            column="57"
            startOffset="17429"
            endLine="372"
            endColumn="68"
            endOffset="17440"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_categories.xml"
            line="378"
            column="57"
            startOffset="17755"
            endLine="378"
            endColumn="68"
            endOffset="17766"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;20dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;20dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_main.xml"
            line="6"
            column="5"
            startOffset="258"
            endLine="6"
            endColumn="30"
            endOffset="283"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;20dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;20dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-sw600dp-land/activity_main.xml"
            line="12"
            column="9"
            startOffset="437"
            endLine="12"
            endColumn="34"
            endOffset="462"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;20dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginEnd=&quot;20dp&quot;"
            oldString="layout_marginRight"
            replacement="layout_marginEnd"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-xlarge-land/activity_main.xml"
            line="13"
            column="9"
            startOffset="486"
            endLine="13"
            endColumn="35"
            endOffset="512"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

</incidents>
