<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/bottom_navigation_container"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:layout_gravity="bottom"
    android:orientation="horizontal"
    android:background="@drawable/bottom_nav_border"
    android:elevation="16dp"
    android:visibility="visible"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:gravity="center_vertical"
    android:layoutDirection="locale">





    <!-- Gallery Tab -->
    <LinearLayout
        android:id="@+id/nav_gallery"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        android:padding="8dp">

        <androidx.cardview.widget.CardView
            android:id="@+id/nav_gallery_icon"
            android:layout_width="36dp"
            android:layout_height="36dp"
            app:cardCornerRadius="18dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/nav_gallery_color">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_gallery_modern"
                android:tint="@android:color/white" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/nav_gallery_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/nav_gallery"
            android:textColor="@color/text_primary"
            android:textSize="10sp"
            android:fontFamily="@font/blabeloo"
            android:gravity="center" />

        <View
            android:id="@+id/nav_gallery_indicator"
            android:layout_width="24dp"
            android:layout_height="2dp"
            android:layout_marginTop="2dp"
            android:background="@color/nav_gallery_color"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Favorites Tab -->
    <LinearLayout
        android:id="@+id/nav_favorites"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        android:padding="8dp">

        <androidx.cardview.widget.CardView
            android:id="@+id/nav_favorites_icon"
            android:layout_width="36dp"
            android:layout_height="36dp"
            app:cardCornerRadius="18dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/nav_favorites_color">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_favorites_modern"
                android:tint="@android:color/white" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/nav_favorites_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/nav_favorites"
            android:textColor="@color/text_primary"
            android:textSize="10sp"
            android:fontFamily="@font/blabeloo"
            android:gravity="center" />

        <View
            android:id="@+id/nav_favorites_indicator"
            android:layout_width="24dp"
            android:layout_height="2dp"
            android:layout_marginTop="2dp"
            android:background="@color/nav_favorites_color"
            android:visibility="gone" />

    </LinearLayout>

    <!-- More Tab -->
    <LinearLayout
        android:id="@+id/nav_more"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        android:padding="8dp">

        <androidx.cardview.widget.CardView
            android:id="@+id/nav_more_icon"
            android:layout_width="36dp"
            android:layout_height="36dp"
            app:cardCornerRadius="18dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/nav_more_color">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_more_modern"
                android:tint="@android:color/white" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/nav_more_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/nav_more"
            android:textColor="@color/text_primary"
            android:textSize="10sp"
            android:fontFamily="@font/blabeloo"
            android:gravity="center" />

        <View
            android:id="@+id/nav_more_indicator"
            android:layout_width="24dp"
            android:layout_height="2dp"
            android:layout_marginTop="2dp"
            android:background="@color/nav_more_color"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
