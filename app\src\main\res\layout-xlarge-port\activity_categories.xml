<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/divider"
    android:gravity="center_vertical"
    android:layoutDirection="locale"
    android:orientation="vertical"
    tools:context=".Categories">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_weight="1"
            android:gravity="center_horizontal|center_vertical"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                card_view:cardCornerRadius="5dp"
                card_view:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:weightSum="3">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="3"
                        android:orientation="vertical">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal">

                            <ImageView
                                android:id="@+id/c_1"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp1_press" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="left|bottom"
                                android:alpha=".7"
                                android:background="@color/colorPrimaryDark"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/text_card_name1"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingBottom="10dp"
                                    android:paddingStart="15dp"
                                    android:paddingTop="20dp"
                                    android:text="@string/flowers"
                                    android:textColor="#ffffff"
                                    android:textSize="35sp"
                                    android:textStyle="bold"
                                    tools:ignore="RtlCompat" />
                            </LinearLayout>
                        </FrameLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"

                card_view:cardCornerRadius="5dp"
                card_view:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:weightSum="3">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dip"
                        android:layout_weight="3"
                        android:orientation="vertical">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal">

                            <ImageView
                                android:id="@+id/c_3"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp3_press" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="left|bottom"
                                android:alpha=".7"
                                android:background="@color/colorPrimaryDark"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/text_card_name2"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingBottom="10dp"
                                    android:paddingStart="15dp"
                                    android:paddingTop="20dp"
                                    android:text="@string/animals"
                                    android:textColor="#ffffff"
                                    android:textSize="35sp"
                                    android:textStyle="bold"

                                    tools:ignore="RtlCompat" />
                            </LinearLayout>
                        </FrameLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                card_view:cardCornerRadius="5dp"
                card_view:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:weightSum="3">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dip"
                        android:layout_weight="3"
                        android:orientation="vertical">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal">

                            <ImageView
                                android:id="@+id/c_5"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp5_press" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="left|bottom"
                                android:alpha=".7"
                                android:background="@color/colorPrimaryDark"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/text_card_name3"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingBottom="10dp"
                                    android:paddingStart="15dp"
                                    android:paddingTop="20dp"
                                    android:text="@string/transport"
                                    android:textColor="#ffffff"
                                    android:textSize="35sp"
                                    android:textStyle="bold"
                                    tools:ignore="RtlCompat" />
                            </LinearLayout>
                        </FrameLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_weight="1"
            android:gravity="center_horizontal|center_vertical"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                card_view:cardCornerRadius="5dp"
                card_view:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:weightSum="3">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dip"
                        android:layout_weight="3"
                        android:orientation="vertical">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal">

                            <ImageView
                                android:id="@+id/c_2"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp2_press" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="left|bottom"
                                android:alpha=".7"
                                android:background="@color/colorPrimaryDark"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/text_card_name4"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingBottom="10dp"
                                    android:paddingStart="15dp"
                                    android:paddingTop="20dp"
                                    android:text="@string/cartoons"
                                    android:textColor="#ffffff"
                                    android:textSize="35sp"
                                    android:textStyle="bold"
                                    tools:ignore="RtlCompat" />
                            </LinearLayout>
                        </FrameLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                card_view:cardCornerRadius="5dp"
                card_view:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:weightSum="3">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dip"
                        android:layout_weight="3"
                        android:orientation="vertical">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal">

                            <ImageView
                                android:id="@+id/c_4"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp4_press" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="left|bottom"
                                android:alpha=".7"
                                android:background="@color/colorPrimaryDark"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/text_card_name5"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingBottom="10dp"
                                    android:paddingStart="15dp"
                                    android:paddingTop="20dp"
                                    android:text="@string/foods"
                                    android:textColor="#ffffff"
                                    android:textSize="35sp"
                                    android:textStyle="bold"
                                    tools:ignore="RtlCompat" />
                            </LinearLayout>
                        </FrameLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                card_view:cardCornerRadius="5dp"
                card_view:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:weightSum="3">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dip"
                        android:layout_weight="3"
                        android:orientation="vertical">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal">

                            <ImageView
                                android:id="@+id/c_6"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:scaleType="centerCrop"
                                android:src="@drawable/gp6_press" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="left|bottom"
                                android:alpha=".7"
                                android:background="@color/colorPrimaryDark"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/text_card_name6"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="15dp"
                                    android:paddingBottom="10dp"
                                    android:paddingTop="20dp"
                                    android:text="@string/nature"
                                    android:textColor="#ffffff"
                                    android:textSize="35sp"
                                    android:textStyle="bold"
                                    tools:ignore="RtlCompat" />
                            </LinearLayout>
                        </FrameLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

    </LinearLayout>

    <!-- Bottom Ad Banner - Fixed positioning -->
    <com.google.android.gms.ads.AdView
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:background="@android:color/white"
        android:elevation="4dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id"/>
</LinearLayout>


