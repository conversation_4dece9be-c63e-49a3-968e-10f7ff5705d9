<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modern_background">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/modern_gradient_background"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:paddingBottom="96dp">

            <!-- Header Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/gallery_header_background"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginBottom="16dp"
                        android:src="@drawable/ic_gallery_modern"
                        android:tint="@android:color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/gallery_title"
                        android:textColor="@android:color/white"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/gallery_subtitle"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:fontFamily="@font/blabeloo"
                        android:alpha="0.9" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Empty State -->
            <androidx.cardview.widget.CardView
                android:id="@+id/empty_state_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="40dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_marginBottom="24dp"
                        android:src="@drawable/ic_empty_gallery"
                        android:tint="@color/text_secondary"
                        android:alpha="0.6" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/no_artworks_yet"
                        android:textColor="@color/text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/start_coloring_save_message"
                        android:textColor="@color/text_secondary"
                        android:textSize="16sp"
                        android:fontFamily="@font/blabeloo"
                        android:gravity="center"
                        android:layout_marginBottom="24dp" />

                    <!-- Start Coloring Button -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/btn_start_coloring"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:foreground="@drawable/beautiful_ripple_effect"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="25dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@color/modern_primary">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="16dp"
                            android:gravity="center_vertical">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_marginEnd="12dp"
                                android:src="@drawable/ic_flowers_modern"
                                android:tint="@android:color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/start_coloring_button"
                                android:textColor="@android:color/white"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Gallery Grid (Hidden when empty) -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/gallery_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2" />

            <!-- Gallery Actions (Hidden when empty) -->
            <LinearLayout
                android:id="@+id/gallery_actions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="16dp"
                android:visibility="gone">

                <!-- Clear All Button -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/btn_clear_all"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:foreground="@drawable/beautiful_ripple_effect"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="@color/modern_surface">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginEnd="8dp"
                            android:src="@drawable/ic_delete_modern"
                            android:tint="@color/text_secondary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/clear_all"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Share All Button -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/btn_share_all"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:foreground="@drawable/beautiful_ripple_effect"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="@color/modern_primary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginEnd="8dp"
                            android:src="@drawable/ic_share_modern"
                            android:tint="@android:color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/share_all"
                            android:textColor="@android:color/white"
                            android:textSize="14sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Navigation -->
    <include
        layout="@layout/simple_bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
